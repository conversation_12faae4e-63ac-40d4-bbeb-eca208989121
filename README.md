# BarcodeCafe-QR-Menu 🍽️

A comprehensive digital menu and restaurant management system built with Next.js 15, React 19, and Firebase. Features QR code-based menu access, complete order management, and full bilingual support (English/Arabic).

<p align="center">
  <img src="public/logo-with-text.svg" alt="Barcode Cafe Logo" width="400">
</p>

## 🚀 Project Status: Production Ready (~99% Complete)

This project is a fully functional restaurant management system optimized for **cash-only operations** with comprehensive bilingual support.

## ✨ Key Features

### 🌐 **Internationalization & Accessibility**
- **Complete bilingual support** (English & Arabic)
- **RTL/LTR layout support** with automatic text direction
- **Dynamic Arabic translations** for all menu content
- **Responsive design** for all devices (mobile-first)
- **Dark & Light mode** with system preference detection
- **Professional UI/UX** with consistent design system

### 🍽️ **Menu Management System** (100% Complete)
- **Dynamic menu categories** with Arabic translations
- **Comprehensive menu items** with nutritional information
- **Advanced search functionality** with bilingual support
- **Stock management** with real-time status updates
- **Image upload** with Firebase Storage integration
- **Nutritional transparency** (caffeine, ingredients, allergens)
- **Delivery availability** validation and indicators

### 👥 **Customer Experience**
- **QR code menu access** for contactless ordering
- **User authentication** (email/password, Google OAuth)
- **Personalized dashboard** with order history
- **Shopping cart** with real-time calculations
- **Order tracking** with status updates
- **Receipt printing** with PDF download
- **Order cancellation** (5-minute customer window)
- **Address management** for delivery orders

### 🛠️ **Admin Management System** (100% Complete)
- **Complete order management** with filtering and search
- **Order editing capability** with audit trail
- **Menu items/categories CRUD** with bilingual forms
- **User management** and admin controls
- **Delivery zone management** with fee calculation
- **Real-time order status updates**
- **Comprehensive reporting** and analytics
- **Receipt customization** (Standard Coffee House format)

### 💰 **Cash-Only Payment System** (100% Complete)
- **Simplified checkout flow** (Cart → Delivery → Confirm)
- **No payment gateway complexity** - optimized for cash operations
- **Order confirmation** with cash payment instructions
- **Professional receipt generation** for cash transactions

## 🛡️ **Order Management & Security**
- **Order cancellation system** with time-based rules
- **Admin order editing** with comprehensive audit trail
- **Delivery availability validation** to prevent operational issues
- **Stock management** with automatic status calculation
- **User authentication** with role-based access control

## 🧾 **Receipt & Documentation**
- **Professional receipt printing** (Standard Coffee House format)
- **PDF generation** with jsPDF for reliable downloads
- **Thermal printer compatibility** for kitchen operations
- **Complete audit trails** for all order modifications

## 🔧 Tech Stack

### **Frontend**
- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **UI Library**: [React 19](https://react.dev/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) with custom design system
- **Icons**: [Font Awesome](https://fontawesome.com/) comprehensive icon set
- **Typography**: [Inter](https://fonts.google.com/specimen/Inter) font family

### **Backend & Database**
- **Database**: [Firebase Firestore](https://firebase.google.com/docs/firestore) (NoSQL)
- **Authentication**: [Firebase Auth](https://firebase.google.com/docs/auth) with email/Google
- **Storage**: [Firebase Storage](https://firebase.google.com/docs/storage) for images
- **Real-time**: Firestore real-time listeners

### **Development & Tools**
- **Language**: [TypeScript](https://www.typescriptlang.org/) for type safety
- **State Management**: React Context API with custom hooks
- **Internationalization**: Custom i18n implementation with RTL support
- **PDF Generation**: [jsPDF](https://github.com/parallax/jsPDF) for receipts
- **Build Tool**: Next.js built-in bundler with SWC

## 🚀 Getting Started

### Prerequisites

- **Node.js 18+** and npm
- **Firebase project** with Firestore, Auth, and Storage enabled
- **Git** for version control

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/shadiqaddoura/BarcodeCafe-QR-Menu.git
   cd BarcodeCafe-QR-Menu
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   Create a `.env.local` file in the root directory:
   ```env
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   ```

4. **Seed the database (optional):**
   ```bash
   # Clean existing data (if any)
   npm run cleanup:menu-data

   # Seed with sample bilingual data
   npm run seed:menu-items
   ```

5. **Run the development server:**
   ```bash
   npm run dev
   ```

6. **Open [http://localhost:3000](http://localhost:3000)** in your browser.

### 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run seed:menu-items` - Seed database with sample menu data
- `npm run cleanup:menu-data` - Clean existing menu data
- `npm run create-admin` - Create admin user

## 📁 Project Structure

```
BarcodeCafe-QR-Menu/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── admin/              # Admin dashboard pages
│   │   │   ├── categories/     # Category management
│   │   │   ├── menu-items/     # Menu item management
│   │   │   ├── orders/         # Order management
│   │   │   └── delivery-zones/ # Delivery zone management
│   │   ├── customer/           # Customer portal pages
│   │   │   ├── dashboard/      # Customer dashboard
│   │   │   ├── orders/         # Order history
│   │   │   └── profile/        # Profile management
│   │   ├── menu/               # Public menu page
│   │   ├── auth/               # Authentication pages
│   │   └── api/                # API routes (if any)
│   ├── components/             # Reusable components
│   │   ├── admin-dashboard/    # Admin-specific components
│   │   ├── customer-dashboard/ # Customer-specific components
│   │   ├── menu/               # Menu-related components
│   │   ├── checkout/           # Checkout flow components
│   │   ├── receipt/            # Receipt printing components
│   │   └── ui/                 # Generic UI components
│   ├── contexts/               # React Context providers
│   │   ├── AuthContext.tsx     # Authentication context
│   │   ├── CartContext.tsx     # Shopping cart context
│   │   ├── LocaleContext.tsx   # Internationalization context
│   │   └── ThemeContext.tsx    # Dark/light mode context
│   ├── lib/                    # Utility functions
│   │   ├── firebase/           # Firebase configuration & functions
│   │   └── utils/              # Helper utilities
│   ├── locales/                # Translation files
│   │   ├── en.json             # English translations
│   │   └── ar.json             # Arabic translations
│   └── types/                  # TypeScript type definitions
├── scripts/                    # Database seeding scripts
│   ├── seed-menu-items.ts      # Menu data seeding
│   └── cleanup-menu-data.ts    # Data cleanup utility
├── docs/                       # Documentation
│   └── scratchpad.md           # Development notes
├── public/                     # Static assets
├── next.config.js              # Next.js configuration
└── tailwind.config.js          # Tailwind CSS configuration
```

## 🎯 Feature Implementation Status

### ✅ **Completed Features (Production Ready)**

#### **🍽️ Menu Management System** (100%)
- ✅ Complete CRUD operations for menu items and categories
- ✅ Bilingual content management (English/Arabic)
- ✅ Nutritional information (caffeine, ingredients, allergens)
- ✅ Image upload with Firebase Storage
- ✅ Stock management with real-time status
- ✅ Delivery availability validation

#### **🛠️ Admin Dashboard** (100%)
- ✅ Comprehensive order management with filtering/search
- ✅ Order editing with audit trail system
- ✅ Menu items/categories management
- ✅ User management and admin controls
- ✅ Delivery zone management
- ✅ Real-time order status updates

#### **👥 Customer Experience** (100%)
- ✅ User authentication (email/password, Google OAuth)
- ✅ Shopping cart with real-time calculations
- ✅ Order history and tracking
- ✅ Order cancellation (5-minute window)
- ✅ Receipt printing with PDF download
- ✅ Address management

#### **💰 Payment System** (100%)
- ✅ Cash-only payment implementation
- ✅ Simplified checkout flow (2 steps)
- ✅ Order confirmation system
- ✅ Professional receipt generation

#### **🌐 Internationalization** (100%)
- ✅ Complete bilingual support (English/Arabic)
- ✅ RTL/LTR layout support
- ✅ Dynamic content translation
- ✅ Locale-based content switching

#### **🧾 Order Cancellation System** (100%)
- ✅ Time-based cancellation rules (5-minute customer window)
- ✅ Admin cancellation flexibility
- ✅ Comprehensive audit trail
- ✅ Automatic refund calculation

#### **🚚 Delivery Management** (100%)
- ✅ Delivery availability integration
- ✅ Zone-based delivery management
- ✅ Address validation
- ✅ Delivery fee calculation

### 🔮 **Future Enhancements**
- Real-time order status notifications
- Advanced analytics and reporting
- Loyalty program implementation
- Special offers and discounts system
- Customer reviews and ratings

## 🌐 Internationalization

The application features a comprehensive internationalization system:

### **Language Support**
- **English** (default) with LTR layout
- **Arabic** with RTL layout support
- **Dynamic content switching** based on user preference
- **Bilingual menu content** with Arabic translations

### **Technical Implementation**
- Custom i18n system with React Context
- Translation keys organized by feature/component
- RTL-specific styling with Tailwind CSS
- Client-side language detection and persistence
- Server-side rendering support for both languages

### **Content Management**
- **Dynamic Arabic fields** for menu items and categories
- **Bilingual admin forms** for content creation
- **Smart content fallbacks** when translations are missing
- **Professional Arabic translations** for all UI elements

## 🎨 UI/UX Features

### **Design System**
- **Consistent color palette** with dark/light mode support
- **Professional typography** using Inter font family
- **Responsive grid layouts** optimized for all devices
- **Smooth animations** and transitions throughout
- **Accessibility-first** design principles

### **Dark Mode Support**
- **System preference detection** with automatic switching
- **Manual toggle option** with persistence
- **Carefully designed color schemes** for both modes
- **Consistent theming** across all components

### **Empty States**
- **Thoughtfully designed** empty states for all sections
- **Clear call-to-action** buttons and instructions
- **Helpful illustrations** and messaging
- **Consistent styling** with the overall design system

## 🔧 Development

### **Code Quality**
- **TypeScript** for type safety and better developer experience
- **ESLint** configuration for code consistency
- **Component-based architecture** with reusable UI components
- **Custom hooks** for state management and side effects

### **Performance**
- **Next.js App Router** for optimal performance
- **Image optimization** with Next.js Image component
- **Code splitting** and lazy loading
- **Firebase real-time listeners** for efficient data updates

### **Testing & Deployment**
- **Development scripts** for easy local setup
- **Database seeding** with bilingual sample data
- **Environment configuration** for different stages
- **Production-ready** build configuration

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgements

- **Design inspiration** from modern restaurant websites
- **Firebase** for backend infrastructure
- **Next.js team** for the excellent framework
- **Tailwind CSS** for the utility-first CSS framework
- **Font Awesome** for the comprehensive icon library

## 📞 Support

For support, questions, or contributions, please:
1. Check the [documentation](docs/) folder
2. Open an issue on GitHub
3. Contact the development team

---

**Built with ❤️ for the restaurant industry**
