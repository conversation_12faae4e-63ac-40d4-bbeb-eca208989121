/// <reference types="node" />
/**
 * Seed Script for Menu Items
 * 
 * This script inserts sample menu items into Firestore for testing purposes.
 * It uses batch operations for efficiency and checks for duplicates before insertion.
 * 
 * Usage:
 * 1. Run with default admin user: npm run seed:menu-items
 * 2. Run with specific user ID: npm run seed:menu-items -- --userId=YOUR_USER_ID
 */

import * as dotenv from 'dotenv';
// Load from .env.local file
dotenv.config({ path: '.env.local' });

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  query, 
  where, 
  getDocs, 
  writeBatch, 
  doc, 
  serverTimestamp 
} from 'firebase/firestore';
import { StockStatus } from '../src/types/models';
import { randomUUID } from 'crypto';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Log Firebase config for debugging (redacting sensitive info)
console.log('Firebase Config:', {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '******' : 'undefined',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID ? '******' : 'undefined',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID ? '******' : 'undefined'
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Constants
const CATEGORIES_COLLECTION = 'categories';
const MENU_ITEMS_COLLECTION = 'menuItems';
const DEFAULT_ADMIN_ID = '4riKYvfThiM4muMIgb7I0wUL2Jt2'; // Replace with actual default admin ID if needed

// Get user ID from command line arguments or use default
function getUserId(): string {
  const userIdArg = process.argv.find(arg => arg.startsWith('--userId='));
  if (userIdArg) {
    return userIdArg.split('=')[1];
  }
  return DEFAULT_ADMIN_ID;
}

// Helper function to get category descriptions
function getCategoryDescription(categoryName: string): string {
  const descriptions: Record<string, string> = {
    'Hot Beverages': 'Warm and comforting coffee drinks and hot beverages',
    'Cold Beverages': 'Refreshing cold drinks and iced beverages',
    'Desserts': 'Sweet treats and delicious desserts',
    'Sandwiches & Snacks': 'Light meals, sandwiches, and snacks',
    'Breakfast': 'Morning meals and breakfast items'
  };
  return descriptions[categoryName] || '';
}

// Helper function to get Arabic category descriptions
function getCategoryDescriptionAr(categoryName: string): string {
  const descriptions: Record<string, string> = {
    'Hot Beverages': 'مشروبات القهوة الدافئة والمشروبات الساخنة المريحة',
    'Cold Beverages': 'المشروبات الباردة المنعشة والمشروبات المثلجة',
    'Desserts': 'الحلويات اللذيذة والأطباق الحلوة',
    'Sandwiches & Snacks': 'الوجبات الخفيفة والسندويشات والمقبلات',
    'Breakfast': 'وجبات الصباح وأطباق الإفطار'
  };
  return descriptions[categoryName] || '';
}

// Sample menu item data with Arabic translations
const sampleMenuItemsData = [
  // Hot Beverages
  {
    title: 'Cappuccino',
    title_ar: 'كابتشينو',
    description: 'Espresso with a perfect balance of steamed milk and foam',
    description_ar: 'إسبريسو مع توازن مثالي من الحليب المبخر والرغوة',
    price: 4.50,
    image: 'https://images.unsplash.com/photo-1572442388796-11668a67e53d?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    caffeine: '150mg',
    caffeine_ar: '150 ملغ',
    ingredients: 'Espresso, steamed milk, milk foam',
    ingredients_ar: 'إسبريسو، حليب مبخر، رغوة الحليب',
    allergens: 'Contains dairy',
    allergens_ar: 'يحتوي على منتجات الألبان',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryName_ar: 'المشروبات الساخنة',
    categoryIcon: 'fa-mug-hot'
  },
  {
    title: 'Latte',
    title_ar: 'لاتيه',
    description: 'Espresso with steamed milk and a light layer of foam',
    description_ar: 'إسبريسو مع الحليب المبخر وطبقة خفيفة من الرغوة',
    price: 4.25,
    image: 'https://images.unsplash.com/photo-1570968915860-54d5c301fa9f?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    caffeine: '120mg',
    caffeine_ar: '120 ملغ',
    ingredients: 'Espresso, steamed milk, light foam',
    ingredients_ar: 'إسبريسو، حليب مبخر، رغوة خفيفة',
    allergens: 'Contains dairy',
    allergens_ar: 'يحتوي على منتجات الألبان',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryName_ar: 'المشروبات الساخنة',
    categoryIcon: 'fa-mug-hot'
  },
  {
    title: 'Espresso',
    title_ar: 'إسبريسو',
    description: 'Pure, strong coffee shot with a rich crema layer',
    description_ar: 'قهوة قوية ونقية مع طبقة كريما غنية',
    price: 3.00,
    image: 'https://images.unsplash.com/photo-1596952954288-16862d37405b?auto=format&fit=crop&w=800&q=80',
    prepTime: 3,
    caffeine: '200mg',
    caffeine_ar: '200 ملغ',
    ingredients: 'Pure espresso coffee',
    ingredients_ar: 'قهوة إسبريسو نقية',
    allergens: 'None',
    allergens_ar: 'لا يوجد',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryName_ar: 'المشروبات الساخنة',
    categoryIcon: 'fa-mug-hot'
  },
  {
    title: 'Mocha',
    title_ar: 'موكا',
    description: 'Espresso with chocolate, steamed milk and whipped cream',
    description_ar: 'إسبريسو مع الشوكولاتة والحليب المبخر والكريمة المخفوقة',
    price: 4.75,
    image: 'https://images.unsplash.com/photo-1578314675249-a6910f80cc39?auto=format&fit=crop&w=800&q=80',
    prepTime: 6,
    caffeine: '130mg',
    caffeine_ar: '130 ملغ',
    ingredients: 'Espresso, chocolate syrup, steamed milk, whipped cream',
    ingredients_ar: 'إسبريسو، شراب الشوكولاتة، حليب مبخر، كريمة مخفوقة',
    allergens: 'Contains dairy, may contain nuts',
    allergens_ar: 'يحتوي على منتجات الألبان، قد يحتوي على المكسرات',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryName_ar: 'المشروبات الساخنة',
    categoryIcon: 'fa-mug-hot'
  },
  // Cold Beverages
  {
    title: 'Iced Coffee',
    title_ar: 'قهوة مثلجة',
    description: 'Chilled coffee served over ice cubes',
    description_ar: 'قهوة مبردة تُقدم مع مكعبات الثلج',
    price: 3.75,
    image: 'https://images.unsplash.com/photo-1517701604599-bb29b565090c?auto=format&fit=crop&w=800&q=80',
    prepTime: 4,
    caffeine: '95mg',
    caffeine_ar: '95 ملغ',
    ingredients: 'Coffee, ice cubes',
    ingredients_ar: 'قهوة، مكعبات ثلج',
    allergens: 'None',
    allergens_ar: 'لا يوجد',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 75,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryName_ar: 'المشروبات الباردة',
    categoryIcon: 'fa-glass-water'
  },
  {
    title: 'Iced Latte',
    title_ar: 'لاتيه مثلج',
    description: 'Espresso with cold milk served over ice',
    description_ar: 'إسبريسو مع الحليب البارد يُقدم مع الثلج',
    price: 4.50,
    image: 'https://images.unsplash.com/photo-1517701550927-30cf4ba1dba5?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    caffeine: '120mg',
    caffeine_ar: '120 ملغ',
    ingredients: 'Espresso, cold milk, ice',
    ingredients_ar: 'إسبريسو، حليب بارد، ثلج',
    allergens: 'Contains dairy',
    allergens_ar: 'يحتوي على منتجات الألبان',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 75,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryName_ar: 'المشروبات الباردة',
    categoryIcon: 'fa-glass-water'
  },
  {
    title: 'Cold Brew',
    title_ar: 'قهوة باردة مخمرة',
    description: 'Smooth coffee slowly brewed with cold water for 12+ hours',
    description_ar: 'قهوة ناعمة مخمرة ببطء بالماء البارد لأكثر من 12 ساعة',
    price: 4.25,
    image: 'https://images.unsplash.com/photo-1583169192095-e4eeef1a7bc1?auto=format&fit=crop&w=800&q=80',
    prepTime: 3,
    caffeine: '180mg',
    caffeine_ar: '180 ملغ',
    ingredients: 'Cold brew coffee concentrate, water',
    ingredients_ar: 'مركز القهوة الباردة، ماء',
    allergens: 'None',
    allergens_ar: 'لا يوجد',
    stockStatus: StockStatus.LOW_STOCK,
    stockQuantity: 25,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryName_ar: 'المشروبات الباردة',
    categoryIcon: 'fa-glass-water'
  },
  {
    title: 'Fruit Smoothie',
    title_ar: 'عصير فواكه مخلوط',
    description: 'Blended fresh fruits with yogurt and honey',
    description_ar: 'فواكه طازجة مخلوطة مع الزبادي والعسل',
    price: 5.50,
    image: 'https://images.unsplash.com/photo-1505252585461-04db1eb84625?auto=format&fit=crop&w=800&q=80',
    prepTime: 8,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Mixed fresh fruits, yogurt, honey',
    ingredients_ar: 'فواكه طازجة مشكلة، زبادي، عسل',
    allergens: 'Contains dairy, may contain nuts',
    allergens_ar: 'يحتوي على منتجات الألبان، قد يحتوي على المكسرات',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 50,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryName_ar: 'المشروبات الباردة',
    categoryIcon: 'fa-glass-water'
  },
  // Desserts
  {
    title: 'Chocolate Cake',
    title_ar: 'كيكة الشوكولاتة',
    description: 'Rich chocolate cake with a smooth ganache topping',
    description_ar: 'كيكة شوكولاتة غنية مع طبقة غاناش ناعمة',
    price: 5.95,
    image: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    caffeine: 'Low caffeine',
    caffeine_ar: 'كافيين منخفض',
    ingredients: 'Chocolate, flour, eggs, butter, sugar, ganache',
    ingredients_ar: 'شوكولاتة، دقيق، بيض، زبدة، سكر، غاناش',
    allergens: 'Contains gluten, dairy, eggs, may contain nuts',
    allergens_ar: 'يحتوي على الغلوتين، منتجات الألبان، البيض، قد يحتوي على المكسرات',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 20,
    isFeatured: true,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryName_ar: 'الحلويات',
    categoryIcon: 'fa-cake-candles'
  },
  {
    title: 'Cheesecake',
    title_ar: 'تشيز كيك',
    description: 'Creamy New York style cheesecake with berry compote',
    description_ar: 'تشيز كيك كريمي على طريقة نيويورك مع مربى التوت',
    price: 6.50,
    image: 'https://images.unsplash.com/photo-1567327613485-fbc7bf196198?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Cream cheese, graham crackers, berries, sugar',
    ingredients_ar: 'جبن كريمي، بسكويت غراهام، توت، سكر',
    allergens: 'Contains gluten, dairy, eggs',
    allergens_ar: 'يحتوي على الغلوتين، منتجات الألبان، البيض',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 15,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryName_ar: 'الحلويات',
    categoryIcon: 'fa-cake-candles'
  },
  {
    title: 'Tiramisu',
    title_ar: 'تيراميسو',
    description: 'Classic Italian dessert with coffee-soaked ladyfingers and mascarpone',
    description_ar: 'حلوى إيطالية كلاسيكية مع أصابع السيدة المنقوعة بالقهوة والماسكاربوني',
    price: 6.95,
    image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    caffeine: 'Contains caffeine',
    caffeine_ar: 'يحتوي على الكافيين',
    ingredients: 'Ladyfingers, espresso, mascarpone, cocoa powder',
    ingredients_ar: 'أصابع السيدة، إسبريسو، ماسكاربوني، مسحوق الكاكاو',
    allergens: 'Contains gluten, dairy, eggs, alcohol',
    allergens_ar: 'يحتوي على الغلوتين، منتجات الألبان، البيض، كحول',
    stockStatus: StockStatus.LOW_STOCK,
    stockQuantity: 8,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryName_ar: 'الحلويات',
    categoryIcon: 'fa-cake-candles'
  },
  {
    title: 'Apple Pie',
    title_ar: 'فطيرة التفاح',
    description: 'Warm apple pie with a flaky crust, served with ice cream',
    description_ar: 'فطيرة تفاح دافئة مع قشرة مقرمشة، تُقدم مع الآيس كريم',
    price: 5.50,
    image: 'https://images.unsplash.com/photo-1535920527002-b35e96722eb9?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Apples, flour, butter, sugar, cinnamon, ice cream',
    ingredients_ar: 'تفاح، دقيق، زبدة، سكر، قرفة، آيس كريم',
    allergens: 'Contains gluten, dairy, may contain eggs',
    allergens_ar: 'يحتوي على الغلوتين، منتجات الألبان، قد يحتوي على البيض',
    stockStatus: StockStatus.OUT_OF_STOCK,
    stockQuantity: 0,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryName_ar: 'الحلويات',
    categoryIcon: 'fa-cake-candles'
  },
  // Sandwiches & Snacks
  {
    title: 'Avocado Toast',
    title_ar: 'توست الأفوكادو',
    description: 'Toasted artisan bread topped with fresh avocado, salt, and pepper',
    description_ar: 'خبز حرفي محمص مع الأفوكادو الطازج والملح والفلفل',
    price: 7.50,
    image: 'https://images.unsplash.com/photo-1603046891744-76f2e0e1bd3d?auto=format&fit=crop&w=800&q=80',
    prepTime: 10,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Artisan bread, fresh avocado, salt, pepper',
    ingredients_ar: 'خبز حرفي، أفوكادو طازج، ملح، فلفل',
    allergens: 'Contains gluten',
    allergens_ar: 'يحتوي على الغلوتين',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 30,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryName_ar: 'السندويشات والوجبات الخفيفة',
    categoryIcon: 'fa-burger'
  },
  {
    title: 'Chicken Panini',
    title_ar: 'بانيني الدجاج',
    description: 'Grilled chicken with mozzarella, pesto, and tomato on pressed ciabatta',
    description_ar: 'دجاج مشوي مع الموزاريلا والبيستو والطماطم على خبز تشياباتا مضغوط',
    price: 8.95,
    image: 'https://images.unsplash.com/photo-1550507992-eb63ffee0847?auto=format&fit=crop&w=800&q=80',
    prepTime: 12,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Grilled chicken, mozzarella, pesto, tomato, ciabatta bread',
    ingredients_ar: 'دجاج مشوي، موزاريلا، بيستو، طماطم، خبز تشياباتا',
    allergens: 'Contains gluten, dairy, may contain nuts',
    allergens_ar: 'يحتوي على الغلوتين، منتجات الألبان، قد يحتوي على المكسرات',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 25,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryName_ar: 'السندويشات والوجبات الخفيفة',
    categoryIcon: 'fa-burger'
  },
  {
    title: 'Ham & Cheese Croissant',
    title_ar: 'كرواسان اللحم والجبن',
    description: 'Buttery croissant filled with ham and melted Swiss cheese',
    description_ar: 'كرواسان بالزبدة محشو باللحم والجبن السويسري المذاب',
    price: 6.75,
    image: 'https://images.unsplash.com/photo-1608198093002-ad4e005484ec?auto=format&fit=crop&w=800&q=80',
    prepTime: 8,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Croissant, ham, Swiss cheese, butter',
    ingredients_ar: 'كرواسان، لحم، جبن سويسري، زبدة',
    allergens: 'Contains gluten, dairy',
    allergens_ar: 'يحتوي على الغلوتين، منتجات الألبان',
    stockStatus: StockStatus.LOW_STOCK,
    stockQuantity: 12,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryName_ar: 'السندويشات والوجبات الخفيفة',
    categoryIcon: 'fa-burger'
  },
  {
    title: 'Hummus Plate',
    title_ar: 'طبق الحمص',
    description: 'Homemade hummus served with warm pita bread and fresh vegetables',
    description_ar: 'حمص منزلي يُقدم مع خبز البيتا الدافئ والخضار الطازجة',
    price: 7.25,
    image: 'https://images.unsplash.com/photo-1540914124281-342587941389?auto=format&fit=crop&w=800&q=80',
    prepTime: 6,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Chickpeas, tahini, olive oil, pita bread, vegetables',
    ingredients_ar: 'حمص، طحينة، زيت زيتون، خبز بيتا، خضار',
    allergens: 'Contains sesame, gluten',
    allergens_ar: 'يحتوي على السمسم، الغلوتين',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 20,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryName_ar: 'السندويشات والوجبات الخفيفة',
    categoryIcon: 'fa-burger'
  },
  // Breakfast
  {
    title: 'Classic Breakfast',
    title_ar: 'إفطار كلاسيكي',
    description: 'Eggs, bacon, toast, and hash browns',
    description_ar: 'بيض، لحم مقدد، توست، وبطاطس مقلية',
    price: 9.95,
    image: 'https://images.unsplash.com/photo-1533089860892-a7c6f10a081a?auto=format&fit=crop&w=800&q=80',
    prepTime: 15,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Eggs, bacon, bread, potatoes, butter',
    ingredients_ar: 'بيض، لحم مقدد، خبز، بطاطس، زبدة',
    allergens: 'Contains gluten, eggs, may contain dairy',
    allergens_ar: 'يحتوي على الغلوتين، البيض، قد يحتوي على منتجات الألبان',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 40,
    isFeatured: true,
    isAvailableForDelivery: false,
    categoryName: 'Breakfast',
    categoryName_ar: 'الإفطار',
    categoryIcon: 'fa-egg'
  },
  {
    title: 'Pancake Stack',
    title_ar: 'برج البان كيك',
    description: 'Fluffy pancakes with maple syrup and butter',
    description_ar: 'بان كيك رقيق مع شراب القيقب والزبدة',
    price: 8.50,
    image: 'https://images.unsplash.com/photo-1565299543923-37dd37887442?auto=format&fit=crop&w=800&q=80',
    prepTime: 12,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Flour, eggs, milk, maple syrup, butter',
    ingredients_ar: 'دقيق، بيض، حليب، شراب القيقب، زبدة',
    allergens: 'Contains gluten, eggs, dairy',
    allergens_ar: 'يحتوي على الغلوتين، البيض، منتجات الألبان',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 35,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Breakfast',
    categoryName_ar: 'الإفطار',
    categoryIcon: 'fa-egg'
  },
  {
    title: 'Fruit Parfait',
    title_ar: 'بارفيه الفواكه',
    description: 'Layers of yogurt, granola, and fresh seasonal fruits',
    description_ar: 'طبقات من الزبادي والجرانولا والفواكه الموسمية الطازجة',
    price: 6.95,
    image: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Yogurt, granola, seasonal fruits, honey',
    ingredients_ar: 'زبادي، جرانولا، فواكه موسمية، عسل',
    allergens: 'Contains dairy, may contain nuts, gluten',
    allergens_ar: 'يحتوي على منتجات الألبان، قد يحتوي على المكسرات، الغلوتين',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 30,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Breakfast',
    categoryName_ar: 'الإفطار',
    categoryIcon: 'fa-egg'
  },
  {
    title: 'Vegetable Omelette',
    title_ar: 'أومليت الخضار',
    description: 'Three-egg omelette with bell peppers, onions, tomatoes, and cheese',
    description_ar: 'أومليت ثلاث بيضات مع الفلفل الحلو والبصل والطماطم والجبن',
    price: 8.95,
    image: 'https://images.unsplash.com/photo-1510693206972-df098062cb71?auto=format&fit=crop&w=800&q=80',
    prepTime: 14,
    caffeine: 'Caffeine-free',
    caffeine_ar: 'خالي من الكافيين',
    ingredients: 'Eggs, bell peppers, onions, tomatoes, cheese',
    ingredients_ar: 'بيض، فلفل حلو، بصل، طماطم، جبن',
    allergens: 'Contains eggs, dairy',
    allergens_ar: 'يحتوي على البيض، منتجات الألبان',
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 25,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Breakfast',
    categoryName_ar: 'الإفطار',
    categoryIcon: 'fa-egg'
  }
];

/**
 * Seed menu items and categories
 */
async function seedMenuItems() {
  try {
    const userId = getUserId();
    console.log(`🌱 Starting seed process for user: ${userId}`);

    // Step 1: Get existing categories to check for duplicates and map them
    const categoryMap: Record<string, { id: string, count: number }> = {};
    const categoryQuery = query(
      collection(db, CATEGORIES_COLLECTION),
      where('userId', '==', userId)
    );
    
    const existingCategories = await getDocs(categoryQuery);
    existingCategories.forEach(doc => {
      const data = doc.data();
      categoryMap[data.name] = { id: doc.id, count: data.itemCount || 0 };
    });
    
    console.log(`Found ${existingCategories.size} existing categories`);

    // Step 2: Get existing menu items to avoid duplicates
    const menuItemsQuery = query(
      collection(db, MENU_ITEMS_COLLECTION),
      where('userId', '==', userId)
    );
    
    const existingMenuItems = await getDocs(menuItemsQuery);
    const existingMenuItemTitles = new Set<string>();
    existingMenuItems.forEach(doc => {
      existingMenuItemTitles.add(doc.data().title);
    });
    
    console.log(`Found ${existingMenuItems.size} existing menu items`);

    // Step 3: Prepare the batch operations
    const batch = writeBatch(db);
    let newItemsCount = 0;
    let newCategoriesCount = 0;
    const categoryItemCounts: Record<string, number> = {};

    // Step 4: Process each sample menu item
    for (const item of sampleMenuItemsData) {
      // Skip if item with same title already exists
      if (existingMenuItemTitles.has(item.title)) {
        console.log(`Skipping existing item: ${item.title}`);
        continue;
      }

      let categoryId: string;
      
      // Check if category exists, if not create it
      if (categoryMap[item.categoryName]) {
        categoryId = categoryMap[item.categoryName].id;
        categoryItemCounts[categoryId] = (categoryItemCounts[categoryId] || 0) + 1;
      } else {
        // Create a new category with Arabic translations
        categoryId = randomUUID();
        const newCategory = {
          userId,
          name: item.categoryName,
          name_ar: item.categoryName_ar,
          icon: item.categoryIcon,
          description: getCategoryDescription(item.categoryName),
          description_ar: getCategoryDescriptionAr(item.categoryName),
          itemCount: 1,
          isActive: true,
          isVisible: true,
          isFeatured: false,
          displayOrder: Object.keys(categoryMap).length + 1,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };
        
        batch.set(doc(db, CATEGORIES_COLLECTION, categoryId), newCategory);
        categoryMap[item.categoryName] = { id: categoryId, count: 1 };
        categoryItemCounts[categoryId] = 1;
        newCategoriesCount++;
      }

      // Create the menu item with Arabic translations
      const menuItemId = randomUUID();
      const menuItem = {
        userId,
        title: item.title,
        title_ar: item.title_ar,
        description: item.description,
        description_ar: item.description_ar,
        price: item.price,
        categoryId,
        image: item.image,
        stockStatus: item.stockStatus,
        stockQuantity: item.stockQuantity,
        prepTime: item.prepTime,
        caffeine: item.caffeine,
        caffeine_ar: item.caffeine_ar,
        ingredients: item.ingredients,
        ingredients_ar: item.ingredients_ar,
        allergens: item.allergens,
        allergens_ar: item.allergens_ar,
        isActive: true,
        isFeatured: item.isFeatured,
        isAvailableForDelivery: item.isAvailableForDelivery,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      batch.set(doc(db, MENU_ITEMS_COLLECTION, menuItemId), menuItem);
      newItemsCount++;
    }

    // Step 5: Update category item counts for existing categories
    for (const [categoryId, count] of Object.entries(categoryItemCounts)) {
      // Only update if it's an existing category
      if (Object.values(categoryMap).some(cat => cat.id === categoryId)) {
        const categoryRef = doc(db, CATEGORIES_COLLECTION, categoryId);
        const existingCount = Object.values(categoryMap).find(cat => cat.id === categoryId)?.count || 0;
        batch.update(categoryRef, { 
          itemCount: existingCount + count,
          updatedAt: serverTimestamp()
        });
      }
    }

    // Step 6: Commit the batch
    if (newItemsCount > 0 || newCategoriesCount > 0) {
      await batch.commit();
      console.log(`✅ Seed completed successfully!`);
      console.log(`📊 Stats:`);
      console.log(`   - Added ${newItemsCount} new menu items`);
      console.log(`   - Added ${newCategoriesCount} new categories`);
    } else {
      console.log(`ℹ️ No new items to add. All sample data already exists.`);
    }

  } catch (error) {
    console.error('❌ Error seeding menu items:', error);
    process.exit(1);
  }
}

// Run the seed function
seedMenuItems(); 