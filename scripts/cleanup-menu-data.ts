/// <reference types="node" />
/**
 * Cleanup Script for Menu Items and Categories
 * 
 * This script removes all existing menu items and categories from Firestore
 * to prepare for fresh seed data with Arabic translations.
 * 
 * Usage:
 * 1. Run with default admin user: npm run cleanup:menu-data
 * 2. Run with specific user ID: npm run cleanup:menu-data -- --userId=YOUR_USER_ID
 */

import * as dotenv from 'dotenv';
// Load from .env.local file
dotenv.config({ path: '.env.local' });

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  query, 
  where, 
  getDocs, 
  writeBatch, 
  doc 
} from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Log Firebase config for debugging (redacting sensitive info)
console.log('Firebase Config:', {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '******' : 'undefined',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID ? '******' : 'undefined',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID ? '******' : 'undefined'
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Constants
const CATEGORIES_COLLECTION = 'categories';
const MENU_ITEMS_COLLECTION = 'menuItems';
const DEFAULT_ADMIN_ID = '4riKYvfThiM4muMIgb7I0wUL2Jt2'; // Replace with actual default admin ID if needed

// Get user ID from command line arguments or use default
function getUserId(): string {
  const userIdArg = process.argv.find(arg => arg.startsWith('--userId='));
  if (userIdArg) {
    return userIdArg.split('=')[1];
  }
  return DEFAULT_ADMIN_ID;
}

/**
 * Clean up existing menu items and categories
 */
async function cleanupMenuData() {
  try {
    const userId = getUserId();
    console.log(`🧹 Starting cleanup process for user: ${userId}`);

    // Step 1: Get all existing menu items
    const menuItemsQuery = query(
      collection(db, MENU_ITEMS_COLLECTION),
      where('userId', '==', userId)
    );
    
    const existingMenuItems = await getDocs(menuItemsQuery);
    console.log(`Found ${existingMenuItems.size} existing menu items to delete`);

    // Step 2: Get all existing categories
    const categoriesQuery = query(
      collection(db, CATEGORIES_COLLECTION),
      where('userId', '==', userId)
    );
    
    const existingCategories = await getDocs(categoriesQuery);
    console.log(`Found ${existingCategories.size} existing categories to delete`);

    // Step 3: Delete menu items in batches (Firestore batch limit is 500)
    let deletedMenuItems = 0;
    const menuItemDocs = existingMenuItems.docs;
    
    for (let i = 0; i < menuItemDocs.length; i += 500) {
      const batch = writeBatch(db);
      const batchDocs = menuItemDocs.slice(i, i + 500);
      
      batchDocs.forEach(docSnapshot => {
        batch.delete(doc(db, MENU_ITEMS_COLLECTION, docSnapshot.id));
      });
      
      await batch.commit();
      deletedMenuItems += batchDocs.length;
      console.log(`Deleted ${deletedMenuItems}/${menuItemDocs.length} menu items`);
    }

    // Step 4: Delete categories in batches
    let deletedCategories = 0;
    const categoryDocs = existingCategories.docs;
    
    for (let i = 0; i < categoryDocs.length; i += 500) {
      const batch = writeBatch(db);
      const batchDocs = categoryDocs.slice(i, i + 500);
      
      batchDocs.forEach(docSnapshot => {
        batch.delete(doc(db, CATEGORIES_COLLECTION, docSnapshot.id));
      });
      
      await batch.commit();
      deletedCategories += batchDocs.length;
      console.log(`Deleted ${deletedCategories}/${categoryDocs.length} categories`);
    }

    console.log(`✅ Cleanup completed successfully!`);
    console.log(`📊 Summary:`);
    console.log(`   - Deleted ${deletedMenuItems} menu items`);
    console.log(`   - Deleted ${deletedCategories} categories`);
    console.log(`🌱 Ready for fresh seed data with Arabic translations!`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup function
cleanupMenuData();
