// Firestore database models

// User Profile Model
export interface UserProfile {
  uid: string;
  displayName: string;
  email: string;
  photoURL?: string;
  phoneNumber?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  loyaltyPoints: number;
  preferences?: UserPreferences;
}

// Admin Model
export interface Admin {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// User Preferences
export interface UserPreferences {
  language: string;
  darkMode: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

// Order Model
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  status: OrderStatus;
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: PaymentMethod;
  deliveryType?: DeliveryZoneType;
  tableNumber?: string;
  deliveryAddress?: string;
  deliveryZoneId?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  specialInstructions?: string;
  // Discount fields
  appliedDiscounts?: AppliedDiscount[];
  discountAmount?: number; // Total discount applied
  originalSubtotal?: number; // Subtotal before discounts
  // Cancellation fields
  cancellationData?: OrderCancellationData;
  cancellationHistory?: OrderCancellationAuditEntry[];
  editHistory?: OrderEditAuditEntry[];
}

// Order Cancellation Data
export interface OrderCancellationData {
  reason: string;
  cancelledBy: 'customer' | 'admin';
  cancelledByUserId: string;
  cancelledByEmail?: string;
  cancelledAt: Date | string;
  refundAmount?: number;
  refundMethod?: string;
  notes?: string;
}

// Order Cancellation Audit Entry
export interface OrderCancellationAuditEntry {
  timestamp: Date | string;
  userId: string;
  userEmail: string;
  userType: 'customer' | 'admin';
  action: 'order_cancelled';
  reason: string;
  refundAmount?: number;
  refundMethod?: string;
  notes?: string;
}

// Order Edit Audit Entry
export interface OrderEditAuditEntry {
  timestamp: Date | string;
  adminId: string;
  adminEmail: string;
  action: 'item_added' | 'item_removed' | 'item_modified' | 'delivery_updated' | 'instructions_updated';
  details: string;
  oldValue?: any;
  newValue?: any;
}

// Order Item
export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  options?: OrderItemOption[];
}

// Order Item Option
export interface OrderItemOption {
  name: string;
  value: string;
  priceAdjustment: number;
}

// Order Status
export enum OrderStatus {
  ORDER_PLACED = 'order_placed',
  PREPARING = 'preparing',
  READY_FOR_PICKUP = 'ready_for_pickup',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

// Payment Method
export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  CASH = 'cash',
  GIFT_CARD = 'gift_card',
  LOYALTY_POINTS = 'loyalty_points'
}

// Address Model
export interface Address {
  id: string;
  userId: string;
  name: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Gift Card Model
export interface GiftCard {
  id: string;
  userId: string;
  code: string;
  initialBalance: number;
  currentBalance: number;
  expiryDate: Date | string;
  isActive: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Review Model - Order-Based Review System
export interface Review {
  id: string;
  userId: string;
  orderId: string; // Required - link to specific order
  orderNumber?: string; // For easy reference in admin interface
  customerName?: string; // Customer display name for admin view
  customerEmail?: string; // Customer email for admin reference
  rating: number; // 1-5 stars
  comment?: string; // Optional review comment
  isApproved?: boolean; // Admin moderation status
  adminResponse?: string; // Admin can respond to reviews
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Category Model
export interface Category {
  id: string;
  userId: string;
  name: string;
  name_ar?: string; // Arabic translation for category name
  icon: string;
  description?: string;
  description_ar?: string; // Arabic translation for category description
  itemCount: number;
  availableFrom?: string;
  availableTo?: string;
  isActive: boolean;
  isVisible: boolean;
  isFeatured: boolean;
  displayOrder: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Stock Status
export enum StockStatus {
  IN_STOCK = 'in_stock',
  LOW_STOCK = 'low_stock',
  OUT_OF_STOCK = 'out_of_stock'
}

// Menu Item Model
export interface MenuItem {
  id: string;
  userId: string;
  title: string;
  title_ar?: string; // Arabic translation for item name
  description: string;
  description_ar?: string; // Arabic translation for item description
  price: number;
  categoryId: string;
  image: string;
  stockStatus: StockStatus;
  stockQuantity: number;
  prepTime?: number;
  caffeine?: string;
  caffeine_ar?: string; // Arabic translation for caffeine content
  ingredients?: string;
  ingredients_ar?: string; // Arabic translation for ingredients
  allergens?: string;
  allergens_ar?: string; // Arabic translation for allergens
  isActive: boolean;
  isFeatured: boolean;
  isAvailableForDelivery: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Delivery Zone Type
export enum DeliveryZoneType {
  PICK_UP = 'pick_up',
  DELIVERY = 'delivery',
  IN_HOUSE_TABLES = 'in_house_tables'
}

// Delivery Zone Model
export interface DeliveryZone {
  id: string;
  userId: string;
  name: string;
  type: DeliveryZoneType;
  description?: string;
  isActive: boolean;
  // For DELIVERY type
  radius?: number;
  deliveryFee?: number;
  minOrderAmount?: number;
  estimatedDeliveryTime?: number;
  // For IN_HOUSE_TABLES type
  tableNumbers?: string[];
  // Common fields
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Offer Types
export enum OfferType {
  PERCENTAGE = 'percentage', // 10% off
  FIXED_AMOUNT = 'fixed_amount', // SAR 5 off
  FREE_DELIVERY = 'free_delivery', // Free delivery
  BUY_X_GET_Y = 'buy_x_get_y', // Buy 2 get 1 free
  LOYALTY_POINTS = 'loyalty_points', // Loyalty points discount
  PACKAGE_DEAL = 'package_deal' // Combo/Package offers
}

export enum DiscountType {
  ORDER_TOTAL = 'order_total', // Discount on entire order
  CATEGORY = 'category', // Discount on specific category
  MENU_ITEM = 'menu_item', // Discount on specific items
  DELIVERY_FEE = 'delivery_fee' // Discount on delivery fee
}

// Package Item for combo deals
export interface PackageItem {
  menuItemId: string;
  quantity: number;
  isOptional?: boolean; // Customer can choose to exclude
  substitutions?: string[]; // Alternative menu item IDs
}

// Offer Conditions
export interface OfferConditions {
  minOrderAmount?: number; // Minimum order value
  maxDiscountAmount?: number; // Maximum discount cap
  applicableCategories?: string[]; // Category IDs
  applicableMenuItems?: string[]; // Menu item IDs
  deliveryTypesOnly?: DeliveryZoneType[]; // Delivery, pickup, table
  loyaltyPointsRequired?: number; // Minimum loyalty points
  firstOrderOnly?: boolean; // New customers only
  dayOfWeek?: number[]; // 0-6 (Sunday-Saturday)
  timeOfDay?: { start: string; end: string }; // "09:00"-"17:00"

  // Package deal specific conditions
  packageItems?: PackageItem[]; // Items included in package
  packagePrice?: number; // Special package price
  allowCustomization?: boolean; // Allow item substitutions
}

// Main Offer Model
export interface Offer {
  id: string;
  userId: string; // Admin who created it
  name: string;
  name_ar?: string; // Arabic translation
  description: string;
  description_ar?: string; // Arabic translation
  type: OfferType;
  discountType: DiscountType;
  discountValue: number; // Percentage (0-100) or fixed amount

  // Conditions
  conditions: OfferConditions;

  // Validity
  startDate: Date | string;
  endDate: Date | string;
  isActive: boolean;

  // Usage tracking
  usageLimit?: number; // Max total uses
  usageCount: number; // Current usage count
  userUsageLimit?: number; // Max uses per user

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Applied Discount for cart/order integration
export interface AppliedDiscount {
  offerId: string;
  offerName: string;
  offerType: OfferType;
  discountType: DiscountType;
  discountAmount: number; // Calculated discount amount
  originalAmount?: number; // Original price before discount
  appliedAt: Date | string;
}

// QR Style Model for QR Generation System
export interface QRStyle {
  id: string;
  userId: string; // Admin who created it
  name: string; // User-defined name for the style
  description?: string; // Optional description

  // QR Code Properties
  size: number; // QR code size in pixels
  foregroundColor: string; // Hex color for QR code foreground
  backgroundColor: string; // Hex color for QR code background
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H'; // Error correction level
  margin: number; // Margin around QR code

  // Generated QR Data
  qrDataURL: string; // Base64 data URL of generated QR code
  qrSVG: string; // SVG string of generated QR code
  targetURL: string; // URL that QR code points to (menu URL)

  // Usage Statistics
  downloadCount: number; // Number of times downloaded
  printCount: number; // Number of times printed
  lastUsedAt?: Date | string; // Last time this style was used

  // Style Categories
  isTemplate: boolean; // Whether this is a predefined template
  templateCategory?: QRStyleCategory; // Category for templates
  tags?: string[]; // User-defined tags for organization

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// QR Style Categories for templates
export enum QRStyleCategory {
  CLASSIC = 'classic',
  MODERN = 'modern',
  SEASONAL = 'seasonal',
  BRAND = 'brand',
  CUSTOM = 'custom'
}

// QR Style Template for predefined styles
export interface QRStyleTemplate {
  id: string;
  name: string;
  name_ar?: string; // Arabic translation
  description: string;
  description_ar?: string; // Arabic translation
  category: QRStyleCategory;
  foregroundColor: string;
  backgroundColor: string;
  size: number;
  isActive: boolean;
  displayOrder: number;
}