"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";

export default function AdminSidebar() {
  const { t, isClient } = useLocale();
  const pathname = usePathname();
  
  const navigationLinks = [
    {
      name: isClient ? t('admin.dashboard') : 'Dashboard',
      href: '/admin/dashboard',
      icon: 'fa-solid fa-gauge-high'
    },
    {
      name: isClient ? t('admin.menuItems') : 'Menu Items',
      href: '/admin/menu-items',
      icon: 'fa-solid fa-utensils'
    },
    {
      name: isClient ? t('admin.categories') : 'Categories',
      href: '/admin/categories',
      icon: 'fa-solid fa-layer-group'
    },
    {
      name: isClient ? t('admin.orders') : 'Orders',
      href: '/admin/orders',
      icon: 'fa-solid fa-receipt'
    },
    {
      name: isClient ? t('admin.navigation.offersAndDiscounts') : 'Offers & Discounts',
      href: '/admin/offers',
      icon: 'fa-solid fa-tag'
    },
    // {
    //   name: isClient ? t('admin.inventory') : 'Inventory',
    //   href: '/admin/inventory',
    //   icon: 'fa-solid fa-boxes-stacked'
    // },
    {
      name: isClient ? t('admin.deliveryZones.title') : 'Delivery Zones',
      href: '/admin/delivery-zones',
      icon: 'fa-solid fa-truck'
    },
    // {
    //   name: isClient ? t('admin.paymentSettings') : 'Payment Settings',
    //   href: '/admin/payment-settings',
    //   icon: 'fa-solid fa-credit-card'
    // },
    {
      name: isClient ? t('reviews.title') : 'Reviews',
      href: '/admin/reviews',
      icon: 'fa-solid fa-star'
    },
    {
      name: isClient ? t('admin.qrGeneratorMenu') : 'QR Generator',
      href: '/admin/qr-generator',
      icon: 'fa-solid fa-qrcode'
    }
  ];
  
  const utilityLinks = [
    {
      name: isClient ? t('nav.menu') : 'Public Menu',
      href: '/menu',
      icon: 'fa-solid fa-globe'
    }
  ];
  
  return (
    <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {isClient ? t('admin.manageCafe') : 'Manage Cafe'}
        </h2>
      </div>
      <nav className="space-y-2">
        {navigationLinks.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                isActive 
                  ? 'text-[#56999B] dark:text-[#5DBDC0] bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20' 
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-[#242832]'
              }`}
            >
              <i className={`${item.icon} ${isActive ? 'text-[#56999B] dark:text-[#5DBDC0]' : ''}`}></i>
              <span className={isActive ? 'font-medium' : ''}>{item.name}</span>
            </Link>
          );
        })}
        
        {utilityLinks.length > 0 && (
          <>
            <div className="pt-4 pb-2">
              <div className="h-px bg-gray-200 dark:bg-gray-700"></div>
            </div>
            
            {utilityLinks.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                    isActive 
                      ? 'text-[#56999B] dark:text-[#5DBDC0] bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20' 
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-[#242832]'
                  }`}
                >
                  <i className={`${item.icon} ${isActive ? 'text-[#56999B] dark:text-[#5DBDC0]' : ''}`}></i>
                  <span className={isActive ? 'font-medium' : ''}>{item.name}</span>
                </Link>
              );
            })}
          </>
        )}
      </nav>
    </div>
  );
} 