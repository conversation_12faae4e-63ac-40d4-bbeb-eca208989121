"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import { Offer, OfferType, DiscountType, Category, MenuItem } from "@/types/models";
import { createOffer, updateOffer, getUserCategories, getAllActiveMenuItems } from "@/lib/firebase/firestore";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";

interface OfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOfferSaved: (offer: Offer) => void;
  editingOffer?: Offer | null;
}

export default function OfferModal({
  isOpen,
  onClose,
  onOfferSaved,
  editingOffer
}: OfferModalProps) {
  const { t, isClient, locale } = useLocale();
  const { user } = useAuth();
  const isRTL = locale === 'ar';
  
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    name_ar: '',
    description: '',
    description_ar: '',
    type: OfferType.PERCENTAGE,
    discountType: DiscountType.ORDER_TOTAL,
    discountValue: 0,
    startDate: '',
    endDate: '',
    isActive: true,
    usageLimit: '',
    userUsageLimit: '',
    minOrderAmount: '',
    maxDiscountAmount: '',
    applicableCategories: [] as string[],
    applicableMenuItems: [] as string[],
    loyaltyPointsRequired: '',
    firstOrderOnly: false,
    packagePrice: '',
    allowCustomization: false,
  });

  // Load data
  useEffect(() => {
    const loadData = async () => {
      if (!user || !isOpen) return;
      
      try {
        const [categoriesData, menuItemsData] = await Promise.all([
          getUserCategories(user.uid),
          getAllActiveMenuItems(user.uid)
        ]);
        setCategories(categoriesData);
        setMenuItems(menuItemsData);
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, [user, isOpen]);

  // Initialize form with editing data
  useEffect(() => {
    if (editingOffer) {
      const startDate = new Date(editingOffer.startDate);
      const endDate = new Date(editingOffer.endDate);
      
      setFormData({
        name: editingOffer.name,
        name_ar: editingOffer.name_ar || '',
        description: editingOffer.description,
        description_ar: editingOffer.description_ar || '',
        type: editingOffer.type,
        discountType: editingOffer.discountType,
        discountValue: editingOffer.discountValue,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        isActive: editingOffer.isActive,
        usageLimit: editingOffer.usageLimit?.toString() || '',
        userUsageLimit: editingOffer.userUsageLimit?.toString() || '',
        minOrderAmount: editingOffer.conditions.minOrderAmount?.toString() || '',
        maxDiscountAmount: editingOffer.conditions.maxDiscountAmount?.toString() || '',
        applicableCategories: editingOffer.conditions.applicableCategories || [],
        applicableMenuItems: editingOffer.conditions.applicableMenuItems || [],
        loyaltyPointsRequired: editingOffer.conditions.loyaltyPointsRequired?.toString() || '',
        firstOrderOnly: editingOffer.conditions.firstOrderOnly || false,
        packagePrice: editingOffer.conditions.packagePrice?.toString() || '',
        allowCustomization: editingOffer.conditions.allowCustomization || false,
      });
    } else {
      // Reset form for new offer
      const today = new Date().toISOString().split('T')[0];
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      
      setFormData({
        name: '',
        name_ar: '',
        description: '',
        description_ar: '',
        type: OfferType.PERCENTAGE,
        discountType: DiscountType.ORDER_TOTAL,
        discountValue: 0,
        startDate: today,
        endDate: nextMonth.toISOString().split('T')[0],
        isActive: true,
        usageLimit: '',
        userUsageLimit: '',
        minOrderAmount: '',
        maxDiscountAmount: '',
        applicableCategories: [],
        applicableMenuItems: [],
        loyaltyPointsRequired: '',
        firstOrderOnly: false,
        packagePrice: '',
        allowCustomization: false,
      });
    }
  }, [editingOffer, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    // Validation
    if (!formData.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Offer name is required.',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.description.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Offer description is required.',
        variant: 'destructive',
      });
      return;
    }

    if (formData.discountValue <= 0) {
      toast({
        title: 'Validation Error',
        description: 'Discount value must be greater than 0.',
        variant: 'destructive',
      });
      return;
    }

    if (new Date(formData.startDate) >= new Date(formData.endDate)) {
      toast({
        title: 'Validation Error',
        description: 'End date must be after start date.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);

      const offerData = {
        userId: user.uid,
        name: formData.name,
        name_ar: formData.name_ar || undefined,
        description: formData.description,
        description_ar: formData.description_ar || undefined,
        type: formData.type,
        discountType: formData.discountType,
        discountValue: formData.discountValue,
        startDate: new Date(formData.startDate),
        endDate: new Date(formData.endDate),
        isActive: formData.isActive,
        usageLimit: formData.usageLimit ? parseInt(formData.usageLimit) : undefined,
        userUsageLimit: formData.userUsageLimit ? parseInt(formData.userUsageLimit) : undefined,
        conditions: {
          minOrderAmount: formData.minOrderAmount ? parseFloat(formData.minOrderAmount) : undefined,
          maxDiscountAmount: formData.maxDiscountAmount ? parseFloat(formData.maxDiscountAmount) : undefined,
          applicableCategories: formData.applicableCategories.length > 0 ? formData.applicableCategories : undefined,
          applicableMenuItems: formData.applicableMenuItems.length > 0 ? formData.applicableMenuItems : undefined,
          loyaltyPointsRequired: formData.loyaltyPointsRequired ? parseInt(formData.loyaltyPointsRequired) : undefined,
          firstOrderOnly: formData.firstOrderOnly || undefined,
          packagePrice: formData.packagePrice ? parseFloat(formData.packagePrice) : undefined,
          allowCustomization: formData.allowCustomization || undefined,
        }
      };

      let savedOffer: Offer;
      if (editingOffer) {
        savedOffer = await updateOffer(editingOffer.id, offerData);
      } else {
        savedOffer = await createOffer(offerData);
      }

      onOfferSaved(savedOffer);
      toast({
        title: 'Success',
        description: editingOffer ? t('admin.offers.offerUpdatedSuccess') : t('admin.offers.offerCreatedSuccess'),
        variant: 'success',
      });
    } catch (error) {
      console.error('Error saving offer:', error);
      toast({
        title: 'Error',
        description: editingOffer ? t('admin.offers.updateOfferError') : t('admin.offers.createOfferError'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" dir={isRTL ? "rtl" : "ltr"}>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold dark:text-gray-100">
            {editingOffer ? 
              (isClient ? t('admin.offers.editOffer') : 'Edit Offer') : 
              (isClient ? t('admin.offers.createOffer') : 'Create New Offer')
            }
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {isClient ? t('admin.offers.basicInfo') : 'Basic Information'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  {isClient ? t('admin.offers.offerName') : 'Offer Name'} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={isClient ? t('admin.offers.offerNamePlaceholder') : 'e.g., Weekend Special'}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="name_ar">
                  {isClient ? t('admin.offers.offerNameArabic') : 'Offer Name (Arabic)'}
                </Label>
                <Input
                  id="name_ar"
                  value={formData.name_ar}
                  onChange={(e) => handleInputChange('name_ar', e.target.value)}
                  placeholder={isClient ? t('admin.offers.offerNameArabicPlaceholder') : 'e.g., عرض نهاية الأسبوع'}
                  dir="rtl"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="description">
                  {isClient ? t('admin.offers.description') : 'Description'} *
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={isClient ? t('admin.offers.descriptionPlaceholder') : 'Describe your offer...'}
                  rows={3}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description_ar">
                  {isClient ? t('admin.offers.descriptionArabic') : 'Description (Arabic)'}
                </Label>
                <Textarea
                  id="description_ar"
                  value={formData.description_ar}
                  onChange={(e) => handleInputChange('description_ar', e.target.value)}
                  placeholder={isClient ? t('admin.offers.descriptionArabicPlaceholder') : 'وصف العرض...'}
                  rows={3}
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Discount Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {isClient ? t('admin.offers.discountConfig') : 'Discount Configuration'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">
                  {isClient ? t('admin.offers.offerType') : 'Offer Type'} *
                </Label>
                <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={OfferType.PERCENTAGE}>
                      {isClient ? t('admin.offers.types.percentage') : 'Percentage Discount'}
                    </SelectItem>
                    <SelectItem value={OfferType.FIXED_AMOUNT}>
                      {isClient ? t('admin.offers.types.fixedAmount') : 'Fixed Amount Discount'}
                    </SelectItem>
                    <SelectItem value={OfferType.FREE_DELIVERY}>
                      {isClient ? t('admin.offers.types.freeDelivery') : 'Free Delivery'}
                    </SelectItem>
                    <SelectItem value={OfferType.PACKAGE_DEAL}>
                      {isClient ? t('admin.offers.types.packageDeal') : 'Package Deal'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="discountType">
                  {isClient ? t('admin.offers.discountType') : 'Apply To'} *
                </Label>
                <Select value={formData.discountType} onValueChange={(value) => handleInputChange('discountType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={DiscountType.ORDER_TOTAL}>
                      {isClient ? t('admin.offers.discountTypes.orderTotal') : 'Entire Order'}
                    </SelectItem>
                    <SelectItem value={DiscountType.CATEGORY}>
                      {isClient ? t('admin.offers.discountTypes.category') : 'Specific Categories'}
                    </SelectItem>
                    <SelectItem value={DiscountType.MENU_ITEM}>
                      {isClient ? t('admin.offers.discountTypes.menuItem') : 'Specific Items'}
                    </SelectItem>
                    <SelectItem value={DiscountType.DELIVERY_FEE}>
                      {isClient ? t('admin.offers.discountTypes.deliveryFee') : 'Delivery Fee'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="discountValue">
                  {formData.type === OfferType.PERCENTAGE ?
                    (isClient ? t('admin.offers.discountPercentage') : 'Discount Percentage') :
                    formData.type === OfferType.FIXED_AMOUNT ?
                    (isClient ? t('admin.offers.discountAmount') : 'Discount Amount (SAR)') :
                    formData.type === OfferType.PACKAGE_DEAL ?
                    (isClient ? t('admin.offers.packagePrice') : 'Package Price (SAR)') :
                    (isClient ? t('admin.offers.discountValue') : 'Discount Value')
                  } *
                </Label>
                <Input
                  id="discountValue"
                  type="number"
                  min="0"
                  step={formData.type === OfferType.PERCENTAGE ? "1" : "0.01"}
                  max={formData.type === OfferType.PERCENTAGE ? "100" : undefined}
                  value={formData.discountValue}
                  onChange={(e) => handleInputChange('discountValue', parseFloat(e.target.value) || 0)}
                  placeholder={formData.type === OfferType.PERCENTAGE ? "10" : "25.00"}
                  required
                />
              </div>
            </div>

            {/* Package Deal specific fields */}
            {formData.type === OfferType.PACKAGE_DEAL && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div className="space-y-2">
                  <Label htmlFor="packagePrice">
                    {isClient ? t('admin.offers.packagePrice') : 'Package Price (SAR)'} *
                  </Label>
                  <Input
                    id="packagePrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.packagePrice}
                    onChange={(e) => handleInputChange('packagePrice', e.target.value)}
                    placeholder="45.00"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="allowCustomization"
                    checked={formData.allowCustomization}
                    onCheckedChange={(checked) => handleInputChange('allowCustomization', checked)}
                  />
                  <Label htmlFor="allowCustomization">
                    {isClient ? t('admin.offers.allowCustomization') : 'Allow Item Substitutions'}
                  </Label>
                </div>
              </div>
            )}
          </div>

          {/* Validity Period */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {isClient ? t('admin.offers.validityPeriod') : 'Validity Period'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">
                  {isClient ? t('admin.offers.startDate') : 'Start Date'} *
                </Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">
                  {isClient ? t('admin.offers.endDate') : 'End Date'} *
                </Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                />
                <Label htmlFor="isActive">
                  {isClient ? t('admin.offers.isActive') : 'Active'}
                </Label>
              </div>
            </div>
          </div>

          {/* Usage Limits */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {isClient ? t('admin.offers.usageLimits') : 'Usage Limits'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="usageLimit">
                  {isClient ? t('admin.offers.totalUsageLimit') : 'Total Usage Limit'}
                </Label>
                <Input
                  id="usageLimit"
                  type="number"
                  min="1"
                  value={formData.usageLimit}
                  onChange={(e) => handleInputChange('usageLimit', e.target.value)}
                  placeholder={isClient ? t('admin.offers.unlimited') : 'Unlimited'}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="userUsageLimit">
                  {isClient ? t('admin.offers.perUserLimit') : 'Per User Limit'}
                </Label>
                <Input
                  id="userUsageLimit"
                  type="number"
                  min="1"
                  value={formData.userUsageLimit}
                  onChange={(e) => handleInputChange('userUsageLimit', e.target.value)}
                  placeholder={isClient ? t('admin.offers.unlimited') : 'Unlimited'}
                />
              </div>
            </div>
          </div>

          {/* Conditions */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {isClient ? t('admin.offers.conditions') : 'Conditions'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minOrderAmount">
                  {isClient ? t('admin.offers.minOrderAmount') : 'Minimum Order Amount (SAR)'}
                </Label>
                <Input
                  id="minOrderAmount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.minOrderAmount}
                  onChange={(e) => handleInputChange('minOrderAmount', e.target.value)}
                  placeholder="50.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxDiscountAmount">
                  {isClient ? t('admin.offers.maxDiscountAmount') : 'Maximum Discount Cap (SAR)'}
                </Label>
                <Input
                  id="maxDiscountAmount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.maxDiscountAmount}
                  onChange={(e) => handleInputChange('maxDiscountAmount', e.target.value)}
                  placeholder="100.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="loyaltyPointsRequired">
                  {isClient ? t('admin.offers.loyaltyPointsRequired') : 'Loyalty Points Required'}
                </Label>
                <Input
                  id="loyaltyPointsRequired"
                  type="number"
                  min="0"
                  value={formData.loyaltyPointsRequired}
                  onChange={(e) => handleInputChange('loyaltyPointsRequired', e.target.value)}
                  placeholder="100"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="firstOrderOnly"
                checked={formData.firstOrderOnly}
                onCheckedChange={(checked) => handleInputChange('firstOrderOnly', checked)}
              />
              <Label htmlFor="firstOrderOnly">
                {isClient ? t('admin.offers.firstOrderOnly') : 'First Order Only (New Customers)'}
              </Label>
            </div>
          </div>

          {/* Applicable Items */}
          {(formData.discountType === DiscountType.CATEGORY || formData.discountType === DiscountType.MENU_ITEM) && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                {formData.discountType === DiscountType.CATEGORY ?
                  (isClient ? t('admin.offers.applicableCategories') : 'Applicable Categories') :
                  (isClient ? t('admin.offers.applicableItems') : 'Applicable Items')
                }
              </h3>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 max-h-40 overflow-y-auto p-4 border rounded-lg">
                {formData.discountType === DiscountType.CATEGORY ?
                  categories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`category-${category.id}`}
                        checked={formData.applicableCategories.includes(category.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleInputChange('applicableCategories', [...formData.applicableCategories, category.id]);
                          } else {
                            handleInputChange('applicableCategories', formData.applicableCategories.filter(id => id !== category.id));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor={`category-${category.id}`} className="text-sm">
                        {locale === 'ar' && category.name_ar ? category.name_ar : category.name}
                      </Label>
                    </div>
                  )) :
                  menuItems.map((item) => (
                    <div key={item.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`item-${item.id}`}
                        checked={formData.applicableMenuItems.includes(item.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleInputChange('applicableMenuItems', [...formData.applicableMenuItems, item.id]);
                          } else {
                            handleInputChange('applicableMenuItems', formData.applicableMenuItems.filter(id => id !== item.id));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor={`item-${item.id}`} className="text-sm">
                        {locale === 'ar' && item.title_ar ? item.title_ar : item.title}
                      </Label>
                    </div>
                  ))
                }
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              {isClient ? t('common.cancel') : 'Cancel'}
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-[#56999B] hover:bg-[#56999B]/90 dark:bg-[#5DBDC0] dark:hover:bg-[#5DBDC0]/90"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>{isClient ? t('common.saving') : 'Saving...'}</span>
                </div>
              ) : (
                editingOffer ?
                  (isClient ? t('admin.offers.updateOffer') : 'Update Offer') :
                  (isClient ? t('admin.offers.createOffer') : 'Create Offer')
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
