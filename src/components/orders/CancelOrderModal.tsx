"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import { Order, OrderStatus, OrderCancellationData } from "@/types/models";
import {
  cancelOrder,
  canCancelOrder,
  calculateRefundAmount,
  getRefundMethod,
  CANCELLATION_REASONS
} from "@/lib/firebase/firestore";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";

interface CancelOrderModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderCancelled: (cancelledOrder: Order) => void;
  userType?: 'customer' | 'admin';
}

export default function CancelOrderModal({
  order,
  isOpen,
  onClose,
  onOrderCancelled,
  userType = 'customer'
}: CancelOrderModalProps) {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  const [isCancelling, setIsCancelling] = useState(false);
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [notes, setNotes] = useState('');
  const [canCancel, setCanCancel] = useState(true);
  const [cancellationError, setCancellationError] = useState<string>('');

  // Check if order can be cancelled when modal opens
  useEffect(() => {
    if (order && isOpen) {
      canCancelOrder(order).then(({ canCancel: canCancelResult, reason }) => {
        setCanCancel(canCancelResult);
        setCancellationError(reason || '');
      });
    }
  }, [order, isOpen]);

  if (!order || !user) return null;

  const refundAmount = calculateRefundAmount(order, new Date());
  const refundMethod = getRefundMethod(order);
  const reasons = userType === 'admin' ? CANCELLATION_REASONS.ADMIN : CANCELLATION_REASONS.CUSTOMER;

  const getCancellationReasonLabel = (reason: string): string => {
    const reasonKey = `orders.cancellation.reasons.${reason}`;
    return isClient ? t(reasonKey) : reason.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getRefundMethodLabel = (method: string): string => {
    const methodKey = `orders.cancellation.refundMethods.${method}`;
    return isClient ? t(methodKey) : method.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const handleCancel = async () => {
    if (!selectedReason) {
      toast({
        title: isClient ? t("orders.cancellation.error") : "Error",
        description: isClient ? t("orders.cancellation.reasonRequired") : "Please select a cancellation reason",
        variant: "destructive"
      });
      return;
    }

    setIsCancelling(true);
    try {
      const cancellationData: OrderCancellationData = {
        reason: selectedReason,
        cancelledBy: userType,
        cancelledByUserId: user.uid,
        cancelledByEmail: user.email || '',
        cancelledAt: new Date(),
        refundAmount,
        refundMethod,
        ...(notes.trim() && { notes: notes.trim() })
      };

      const cancelledOrder = await cancelOrder(order.id, cancellationData);
      
      onOrderCancelled(cancelledOrder);
      onClose();
      
      toast({
        title: isClient ? t("orders.cancellation.success") : "Order Cancelled",
        description: isClient ? t("orders.cancellation.successMessage") : "The order has been successfully cancelled",
        variant: "default"
      });
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast({
        title: isClient ? t("orders.cancellation.error") : "Error",
        description: error instanceof Error ? error.message : "Failed to cancel order",
        variant: "destructive"
      });
    } finally {
      setIsCancelling(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-red-600 dark:text-red-400">
            {isClient ? t("orders.cancellation.title") : "Cancel Order"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Order Information */}
          <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {isClient ? t("orders.orderNumber") : "Order Number"}: #{order.id.slice(-8).toUpperCase()}
            </p>
            <p className="text-sm font-medium dark:text-gray-100">
              {isClient ? t("orders.total") : "Total"}: {isClient ? t('common.currency') : 'SAR'} {order.total.toFixed(2)}
            </p>
          </div>

          {!canCancel ? (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-600 dark:text-red-400 text-sm">
                {cancellationError}
              </p>
            </div>
          ) : (
            <>
              {/* Cancellation Reason */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isClient ? t("orders.cancellation.reason") : "Cancellation Reason"} *
                </label>
                <Select value={selectedReason} onValueChange={setSelectedReason}>
                  <SelectTrigger>
                    <SelectValue placeholder={isClient ? t("orders.cancellation.selectReason") : "Select a reason"} />
                  </SelectTrigger>
                  <SelectContent>
                    {reasons.map((reason) => (
                      <SelectItem key={reason} value={reason}>
                        {getCancellationReasonLabel(reason)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Additional Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isClient ? t("orders.cancellation.notes") : "Additional Notes"} ({isClient ? t("common.optional") : "Optional"})
                </label>
                <Textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder={isClient ? t("orders.cancellation.notesPlaceholder") : "Any additional information..."}
                  rows={3}
                />
              </div>

              {/* Refund Information */}
              {refundAmount > 0 && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-green-800 dark:text-green-400 mb-2">
                    {isClient ? t("orders.cancellation.refundInfo") : "Refund Information"}
                  </h4>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    {isClient ? t("orders.cancellation.refundAmount") : "Refund Amount"}: {isClient ? t('common.currency') : 'SAR'} {refundAmount.toFixed(2)}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    {isClient ? t("orders.cancellation.refundMethod") : "Refund Method"}: {getRefundMethodLabel(refundMethod)}
                  </p>
                </div>
              )}

              {/* Warning */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <p className="text-yellow-800 dark:text-yellow-400 text-sm">
                  {isClient ? t("orders.cancellation.warning") : "This action cannot be undone. The order will be permanently cancelled."}
                </p>
              </div>
            </>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
          <Button variant="outline" onClick={onClose}>
            {isClient ? t("common.cancel") : "Cancel"}
          </Button>
          {canCancel && (
            <Button
              onClick={handleCancel}
              disabled={isCancelling || !selectedReason}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isCancelling ? (
                <>
                  <i className="fa-solid fa-spinner fa-spin mr-2"></i>
                  {isClient ? t("orders.cancellation.cancelling") : "Cancelling..."}
                </>
              ) : (
                <>
                  <i className="fa-solid fa-ban mr-2"></i>
                  {isClient ? t("orders.cancellation.confirmCancel") : "Confirm Cancellation"}
                </>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
