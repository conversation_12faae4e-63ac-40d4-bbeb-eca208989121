"use client";

import { useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { Offer, OfferType, MenuItem } from "@/types/models";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface PackageCardProps {
  offer: Offer;
  packageItems: MenuItem[];
  onViewDetails: (offer: Offer, packageItems: MenuItem[]) => void;
  onAddToCart: (offer: Offer, packageItems: MenuItem[]) => void;
}

export default function PackageCard({ offer, packageItems, onViewDetails, onAddToCart }: PackageCardProps) {
  const { t, isClient, locale } = useLocale();
  const isRTL = locale === 'ar';

  // Only show package deal offers
  if (offer.type !== OfferType.PACKAGE_DEAL) {
    return null;
  }

  // Calculate original price (sum of individual items)
  const originalPrice = packageItems.reduce((total, item) => {
    const packageItem = offer.conditions.packageItems?.find(pi => pi.menuItemId === item.id);
    const quantity = packageItem?.quantity || 1;
    return total + (item.price * quantity);
  }, 0);

  const packagePrice = offer.conditions.packagePrice || 0;
  const savings = originalPrice - packagePrice;
  const savingsPercentage = originalPrice > 0 ? Math.round((savings / originalPrice) * 100) : 0;

  return (
    <div className="bg-white dark:bg-[#392e23] rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-all hover:translate-y-[-2px] border-2 border-green-200 dark:border-green-800">
      {/* Package Badge */}
      <div className="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <i className="fa-solid fa-box-open"></i>
            <span className="font-medium text-sm">
              {isClient ? t('packages.packageDeal') : 'Package Deal'}
            </span>
          </div>
          {savings > 0 && (
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
              {isClient ? t('packages.save') : 'Save'} {savingsPercentage}%
            </Badge>
          )}
        </div>
      </div>

      <div className="p-4">
        {/* Package Header */}
        <div className="mb-3">
          <h3 className="font-bold text-lg text-[#703f23] dark:text-[#e09a62] mb-1">
            {locale === 'ar' && offer.name_ar ? offer.name_ar : offer.name}
          </h3>
          <p className="text-sm text-[#94795e] dark:text-[#b49678] line-clamp-2">
            {locale === 'ar' && offer.description_ar ? offer.description_ar : offer.description}
          </p>
        </div>

        {/* Package Items Preview */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2 mb-2">
            {packageItems.slice(0, 3).map((item, index) => {
              const packageItem = offer.conditions.packageItems?.find(pi => pi.menuItemId === item.id);
              const quantity = packageItem?.quantity || 1;
              
              return (
                <div key={item.id} className="flex items-center gap-1 text-xs text-[#94795e] dark:text-[#b49678]">
                  {index > 0 && <span className="mx-1">+</span>}
                  <span>
                    {quantity > 1 && `${quantity}× `}
                    {locale === 'ar' && item.title_ar ? item.title_ar : item.title}
                  </span>
                </div>
              );
            })}
            {packageItems.length > 3 && (
              <span className="text-xs text-[#94795e] dark:text-[#b49678]">
                +{packageItems.length - 3} {isClient ? t('packages.moreItems') : 'more'}
              </span>
            )}
          </div>
        </div>

        {/* Pricing */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {isClient ? t('common.currency') : 'SAR'} {packagePrice.toFixed(2)}
                </span>
                {savings > 0 && (
                  <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                    {isClient ? t('common.currency') : 'SAR'} {originalPrice.toFixed(2)}
                  </span>
                )}
              </div>
              {savings > 0 && (
                <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                  {isClient ? t('packages.youSave') : 'You save'} {isClient ? t('common.currency') : 'SAR'} {savings.toFixed(2)}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewDetails(offer, packageItems)}
            className="flex-1 border-[#c27845] dark:border-[#d27d46] text-[#c27845] dark:text-[#d27d46] hover:bg-[#c27845]/10 dark:hover:bg-[#d27d46]/10"
          >
            <i className="fa-solid fa-eye mr-2"></i>
            {isClient ? t('packages.viewDetails') : 'View Details'}
          </Button>
          <Button
            size="sm"
            onClick={() => onAddToCart(offer, packageItems)}
            className="flex-1 bg-[#c27845] dark:bg-[#d27d46] text-white hover:bg-[#b06735] dark:hover:bg-[#c16e37]"
          >
            <i className="fa-solid fa-cart-plus mr-2"></i>
            {isClient ? t('packages.addPackage') : 'Add Package'}
          </Button>
        </div>

        {/* Package Features */}
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs text-[#94795e] dark:text-[#b49678]">
            <div className="flex items-center gap-4">
              {offer.conditions.allowCustomization && (
                <div className="flex items-center gap-1">
                  <i className="fa-solid fa-sliders text-green-500"></i>
                  <span>{isClient ? t('packages.customizable') : 'Customizable'}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <i className="fa-solid fa-users text-blue-500"></i>
                <span>{packageItems.length} {isClient ? t('packages.items') : 'items'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
