'use client';

import { useState } from 'react';
import { useCart } from '@/contexts/CartContext';
import DeliveryOptions from '@/components/checkout/DeliveryOptions';
import { useLocale } from '@/contexts/LocaleContext';
import { useAuth } from '@/contexts/AuthContext';
import { Sheet, SheetContent, SheetTitle } from '@/components/ui/sheet';
import { useRouter } from 'next/navigation';
import { DeliveryType } from '@/types/delivery';
import { Order } from '@/types/models';
import OrderReviewPrompt from '@/components/reviews/OrderReviewPrompt';

export default function CartButton() {
  const { t, isClient, locale } = useLocale();
  const {
    cartItems,
    cartTotal,
    cartItemsCount,
    updateQuantity,
    removeFromCart,
    placeOrder,
    deliveryOption,
    setDeliveryOption,
    totalWithDelivery,
    clearCart,
    hasNonDeliveryItems,
    nonDeliveryItems,
    canSelectDelivery,
    validateDeliveryAvailability,
    // Discount functionality
    appliedDiscounts,
    availableOffers,
    applyOffer,
    removeDiscount,
    totalDiscount,
    finalTotal,
    refreshOffers
  } = useCart();
  const { user } = useAuth();
  const router = useRouter();
  const isRTL = locale === 'ar';
  
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);
  const [checkoutStep, setCheckoutStep] = useState<'cart' | 'delivery'>('cart');
  const [showAvailableOffers, setShowAvailableOffers] = useState(false);
  const [offerCode, setOfferCode] = useState('');
  const [isReviewPromptOpen, setIsReviewPromptOpen] = useState(false);
  const [placedOrder, setPlacedOrder] = useState<Order | null>(null);

  const handleProceedToDelivery = () => {
    setCheckoutStep('delivery');
  };

  const handleBackToCart = () => {
    setCheckoutStep('cart');
  };

  const handleApplyOffer = async (offerId: string) => {
    const success = await applyOffer(offerId);
    if (success) {
      setShowAvailableOffers(false);
    }
  };

  const handleRemoveDiscount = (offerId: string) => {
    removeDiscount(offerId);
  };

  const handleCartOpen = () => {
    setIsCartOpen(true);
    // Refresh offers when cart opens
    if (user) {
      refreshOffers();
    }
  };

  const handlePlaceOrder = async () => {
    if (!user) {
      // Close cart sheet and redirect to login
      setIsCartOpen(false);
      router.push('/signin');
      return;
    }

    setIsPlacingOrder(true);
    try {
      const order = await placeOrder();
      if (order) {
        setPlacedOrder(order);
        setIsCartOpen(false);
        setCheckoutStep('cart'); // Reset checkout step
        setIsReviewPromptOpen(true); // Show review prompt
      }
    } catch (error) {
      console.error('Error placing order:', error);
    } finally {
      setIsPlacingOrder(false);
    }
  };

  const handleReviewSubmitted = () => {
    // After review is submitted, redirect to orders page
    router.push('/customer/orders');
  };

  const handleReviewSkipped = () => {
    // If review is skipped, also redirect to orders page
    router.push('/customer/orders');
  };
  
  if (cartItemsCount === 0) {
    return null;
  }
  
  return (
    <>
      <button
        onClick={handleCartOpen}
        className="fixed bottom-6 right-6 w-16 h-16 rounded-full bg-[#c27845] dark:bg-[#d27d46] text-white flex items-center justify-center shadow-xl hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-all hover:scale-105 z-50"
        aria-label={isClient ? t('menu.viewCart') : 'View Cart'}
      >
        <i className="fa-solid fa-cart-shopping text-xl"></i>
        {cartItemsCount > 0 && (
          <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-7 h-7 flex items-center justify-center shadow-md">
            {cartItemsCount}
          </div>
        )}
      </button>
      
      <Sheet open={isCartOpen} onOpenChange={setIsCartOpen}>
        <SheetContent 
          side={isRTL ? "left" : "right"}
          className="bg-white dark:bg-[#392e23] p-0 overflow-y-auto max-w-md w-full"
        >
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-[#dac6ae] dark:border-[#392e23]">
              <SheetTitle className="text-2xl font-bold text-[#703f23] dark:text-[#e09a62] flex items-center">
                <i className="fa-solid fa-cart-shopping mr-3 text-[#c27845] dark:text-[#d27d46]"></i>
                {isClient ? t('menu.cart') : 'Cart'}
              </SheetTitle>
            </div>
            
            {cartItems.length === 0 ? (
              <div className="flex-grow flex flex-col items-center justify-center p-8">
                <div className="bg-[#f8f5e9] dark:bg-[#2c3436] w-24 h-24 rounded-full flex items-center justify-center mb-6">
                  <i className="fa-solid fa-cart-shopping text-[#c27845]/50 dark:text-[#d27d46]/50 text-4xl"></i>
                </div>
                <h3 className="text-[#703f23] dark:text-[#e09a62] font-semibold text-xl mb-2">
                  {isClient ? t('menu.emptyCart') : 'Your cart is empty'}
                </h3>
                <p className="text-[#94795e] dark:text-[#b49678] text-center max-w-xs">
                  {isClient ? t('menu.emptyCart') : 'Add some delicious items to your cart to get started'}
                </p>
              </div>
            ) : (
              <>
                <div className="flex-grow overflow-auto">
                  <div className="p-6">
                    <div className="space-y-4">
                      {checkoutStep === 'cart' && (
                        <>
                          {cartItems.map(item => (
                            <div key={item.id} className="flex items-center gap-4 pb-4 border-b border-[#dac6ae] dark:border-[#392e23] last:border-0">
                              <div className="flex-shrink-0 w-16 h-16 bg-[#f8f5e9] dark:bg-[#2c3436] rounded-lg overflow-hidden">
                                {item.image && (
                                  <img 
                                    src={item.image} 
                                    alt={item.title} 
                                    className="w-full h-full object-cover"
                                  />
                                )}
                              </div>
                              
                              <div className="flex-grow">
                                <div className="flex justify-between">
                                  <div className="flex-1">
                                    <h3 className="font-semibold text-[#703f23] dark:text-[#e09a62]">
                                      {item.title}
                                    </h3>
                                    {!item.isAvailableForDelivery && (
                                      <div className="flex items-center mt-1">
                                        <i className="fa-solid fa-truck text-xs text-orange-500 mr-1"></i>
                                        <span className="text-xs text-orange-600 dark:text-orange-400">
                                          {isClient ? t('cart.notAvailableForDelivery') : 'Not available for delivery'}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                  <button
                                    onClick={() => removeFromCart(item.id)}
                                    className="text-red-500 dark:text-red-400 p-1 hover:text-red-600 dark:hover:text-red-300 transition-colors"
                                    aria-label={isClient ? t('common.remove') : 'Remove'}
                                  >
                                    <i className="fa-solid fa-times"></i>
                                  </button>
                                </div>
                                
                                <div className="flex justify-between items-center mt-2">
                                  <div className="flex items-center gap-2">
                                    <button 
                                      onClick={() => updateQuantity(item.id, Math.max(1, item.quantity - 1))}
                                      className="w-8 h-8 rounded-full bg-[#f8f5e9] dark:bg-[#2c3436] flex items-center justify-center text-[#703f23] dark:text-[#e09a62]"
                                      aria-label="Decrease quantity"
                                    >
                                      <i className="fa-solid fa-minus text-xs"></i>
                                    </button>
                                    
                                    <span className="font-medium text-[#703f23] dark:text-[#e09a62] min-w-[1.5rem] text-center">
                                      {item.quantity}
                                    </span>
                                    
                                    <button 
                                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                      className="w-8 h-8 rounded-full bg-[#f8f5e9] dark:bg-[#2c3436] flex items-center justify-center text-[#703f23] dark:text-[#e09a62]"
                                      aria-label="Increase quantity"
                                    >
                                      <i className="fa-solid fa-plus text-xs"></i>
                                    </button>
                                  </div>
                                  
                                  <div className="font-medium text-[#703f23] dark:text-[#e09a62]">
                                    {isClient ? t('common.currency') : 'SAR'} {(item.price * item.quantity).toFixed(2)}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}

                          {/* Delivery availability warning */}
                          {checkoutStep === 'cart' && hasNonDeliveryItems && (
                            <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                              <div className="flex items-start gap-2">
                                <i className="fa-solid fa-exclamation-triangle text-orange-500 mt-0.5"></i>
                                <div className="flex-1">
                                  <p className="text-sm text-orange-700 dark:text-orange-300 font-medium">
                                    {isClient ? t('cart.deliveryWarningTitle') : 'Delivery Restriction'}
                                  </p>
                                  <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                                    {isClient ? t('cart.deliveryWarningMessage') : 'Some items in your cart are not available for delivery. You can still order them for pickup or table service.'}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-[#dac6ae] dark:border-[#392e23]">
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-[#94795e] dark:text-[#b49678]">
                        {isClient ? t('menu.subtotal') : 'Subtotal'}
                      </span>
                      <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
                        {isClient ? t('common.currency') : 'SAR'} {cartTotal.toFixed(2)}
                      </span>
                    </div>
                    
                    {deliveryOption?.fee && deliveryOption.fee > 0 && (
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-[#94795e] dark:text-[#b49678]">
                          {isClient ? t('checkout.deliveryFee') : 'Delivery Fee'}
                        </span>
                        <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
                          {isClient ? t('common.currency') : 'SAR'} {deliveryOption.fee.toFixed(2)}
                        </span>
                      </div>
                    )}

                    {/* Applied Discounts */}
                    {appliedDiscounts.length > 0 && (
                      <div className="space-y-2 mb-4">
                        {appliedDiscounts.map((discount) => (
                          <div key={discount.offerId} className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <span className="text-green-600 dark:text-green-400 text-sm">
                                <i className="fa-solid fa-tag mr-1"></i>
                                {discount.offerName}
                              </span>
                              <button
                                onClick={() => handleRemoveDiscount(discount.offerId)}
                                className="text-red-500 hover:text-red-600 text-xs"
                                aria-label="Remove discount"
                              >
                                <i className="fa-solid fa-times"></i>
                              </button>
                            </div>
                            <span className="font-medium text-green-600 dark:text-green-400">
                              -{isClient ? t('common.currency') : 'SAR'} {discount.discountAmount.toFixed(2)}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Available Offers Section */}
                    {checkoutStep === 'cart' && user && availableOffers.length > 0 && (
                      <div className="mb-4">
                        <button
                          onClick={() => setShowAvailableOffers(!showAvailableOffers)}
                          className="w-full flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                        >
                          <div className="flex items-center gap-2">
                            <i className="fa-solid fa-percent"></i>
                            <span className="font-medium">
                              {isClient ? t('cart.availableOffers') : 'Available Offers'} ({availableOffers.length})
                            </span>
                          </div>
                          <i className={`fa-solid fa-chevron-${showAvailableOffers ? 'up' : 'down'}`}></i>
                        </button>

                        {showAvailableOffers && (
                          <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                            {availableOffers.map((offer) => {
                              const isApplied = appliedDiscounts.some(d => d.offerId === offer.id);
                              return (
                                <div
                                  key={offer.id}
                                  className={`p-3 border rounded-lg ${
                                    isApplied
                                      ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                                      : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                                  }`}
                                >
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <h4 className="font-medium text-sm text-gray-800 dark:text-gray-200">
                                        {locale === 'ar' && offer.name_ar ? offer.name_ar : offer.name}
                                      </h4>
                                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                        {locale === 'ar' && offer.description_ar ? offer.description_ar : offer.description}
                                      </p>
                                    </div>
                                    {!isApplied && (
                                      <button
                                        onClick={() => handleApplyOffer(offer.id)}
                                        className="ml-2 px-3 py-1 bg-green-600 text-white text-xs rounded-full hover:bg-green-700 transition-colors"
                                      >
                                        {isClient ? t('cart.apply') : 'Apply'}
                                      </button>
                                    )}
                                    {isApplied && (
                                      <span className="ml-2 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 text-xs rounded-full">
                                        {isClient ? t('cart.applied') : 'Applied'}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center mb-6 pt-2 border-t border-[#dac6ae] dark:border-[#392e23]">
                      <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
                        {isClient ? t('menu.total') : 'Total'}
                      </span>
                      <span className="font-bold text-lg text-[#703f23] dark:text-[#e09a62]">
                        {isClient ? t('common.currency') : 'SAR'} {finalTotal.toFixed(2)}
                      </span>
                    </div>

                    {/* Savings Display */}
                    {totalDiscount > 0 && (
                      <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div className="flex items-center justify-center gap-2 text-green-700 dark:text-green-300">
                          <i className="fa-solid fa-piggy-bank"></i>
                          <span className="font-medium">
                            {isClient ? t('cart.totalSavings') : 'You saved'}: {isClient ? t('common.currency') : 'SAR'} {totalDiscount.toFixed(2)}
                          </span>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex flex-col gap-3">
                      {checkoutStep === 'cart' && (
                        <button
                          onClick={handleProceedToDelivery}
                          className="w-full bg-[#c27845] dark:bg-[#d27d46] text-white py-3 rounded-full font-medium hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-colors flex items-center justify-center"
                        >
                          <i className="fa-solid fa-arrow-right mr-2"></i>
                          {isClient ? t('checkout.proceedToDelivery') : 'Proceed to Delivery Options'}
                        </button>
                      )}
                      
                      {checkoutStep === 'delivery' && (
                        <div className="space-y-4">
                          <DeliveryOptions
                            onDeliveryOptionSelected={setDeliveryOption}
                            selectedOption={deliveryOption || undefined}
                            canSelectDelivery={canSelectDelivery}
                            deliveryValidationMessage={hasNonDeliveryItems ? (isClient ? t('cart.deliveryValidationMessage') : 'Some items in your cart are not available for delivery. Please remove them or choose pickup/table service.') : undefined}
                          />

                          {deliveryOption && (
                            <div className="p-4 bg-[#f8f5e9] dark:bg-[#2c3436] rounded-lg border border-[#dac6ae] dark:border-[#392e23]">
                              <h3 className="font-medium text-[#703f23] dark:text-[#e09a62] mb-2">
                                {isClient ? t('checkout.orderSummary') : 'Order Summary'}
                              </h3>

                              <div className="text-sm text-[#94795e] dark:text-[#b49678] space-y-1">
                                {deliveryOption?.type === DeliveryType.TABLE && deliveryOption.tableZone && deliveryOption.tableNumber && (
                                  <>
                                    <p><strong>{isClient ? t('checkout.tableZone') : 'Table Zone'}:</strong> {deliveryOption.tableZone.name}</p>
                                    <p><strong>{isClient ? t('checkout.tableNumber') : 'Table Number'}:</strong> {deliveryOption.tableNumber}</p>
                                  </>
                                )}

                                {deliveryOption?.type === DeliveryType.PICK_UP && (
                                  <p><strong>{isClient ? t('checkout.deliveryType') : 'Delivery Type'}:</strong> {isClient ? t('checkout.pickUpFromCounter') : 'Pick up from counter'}</p>
                                )}

                                {deliveryOption?.type === DeliveryType.DELIVERY && deliveryOption.deliveryZone && (
                                  <>
                                    <p><strong>{isClient ? t('checkout.deliveryZone') : 'Delivery Zone'}:</strong> {deliveryOption.deliveryZone.name}</p>
                                    <p><strong>{isClient ? t('checkout.deliveryAddress') : 'Delivery Address'}:</strong> {deliveryOption.deliveryAddress}</p>
                                    {deliveryOption.fee && deliveryOption.fee > 0 && (
                                      <p><strong>{isClient ? t('checkout.deliveryFee') : 'Delivery Fee'}:</strong> {isClient ? t('common.currency') : 'SAR'} {deliveryOption.fee.toFixed(2)}</p>
                                    )}
                                  </>
                                )}

                                <div className="pt-2 mt-2 border-t border-[#dac6ae] dark:border-[#392e23]">
                                  <p><strong>{isClient ? t('checkout.paymentMethod') : 'Payment Method'}:</strong> {isClient ? t('checkout.cashOnDelivery') : 'Cash on Delivery/Pickup'}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="flex gap-2 mt-4">
                            <button
                              onClick={handleBackToCart}
                              className="flex-1 border border-[#c27845] dark:border-[#d27d46] text-[#c27845] dark:text-[#d27d46] py-3 rounded-full font-medium hover:bg-[#c27845]/10 dark:hover:bg-[#d27d46]/10 transition-colors flex items-center justify-center"
                            >
                              <i className="fa-solid fa-arrow-left mr-2"></i>
                              {isClient ? t('common.back') : 'Back'}
                            </button>
                            
                            <button
                              onClick={handlePlaceOrder}
                              disabled={
                                isPlacingOrder ||
                                (deliveryOption?.type === DeliveryType.TABLE && !deliveryOption?.tableNumber) ||
                                (deliveryOption?.type === DeliveryType.DELIVERY && (!deliveryOption?.deliveryZone || !deliveryOption?.deliveryAddress))
                              }
                              className="flex-1 bg-[#c27845] dark:bg-[#d27d46] text-white py-3 rounded-full font-medium hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isPlacingOrder ? (
                                <>
                                  <span className="mr-2">{isClient ? t('menu.placingOrder') : 'Placing Order...'}</span>
                                  <i className="fa-solid fa-spinner fa-spin"></i>
                                </>
                              ) : (
                                <>
                                  <i className="fa-solid fa-check-circle mr-2"></i>
                                  {isClient ? t('checkout.confirmOrder') : 'Confirm Order'}
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      )}

                      
                      <button
                        onClick={() => clearCart()}
                        className="w-full py-3 text-[#703f23] dark:text-[#e09a62] font-medium hover:underline transition-all flex items-center justify-center"
                      >
                        <i className="fa-solid fa-trash-can mr-2 text-sm"></i>
                        {isClient ? t('menu.clearCart') : 'Clear Cart'}
                      </button>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Order Review Prompt */}
      <OrderReviewPrompt
        order={placedOrder}
        isOpen={isReviewPromptOpen}
        onClose={handleReviewSkipped}
        onReviewSubmitted={handleReviewSubmitted}
      />
    </>
  );
}
