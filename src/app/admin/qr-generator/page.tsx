"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import {
  generateMenuQRCode,
  downloadQRCodePNG,
  downloadQRCodeSVG,
  printQRCode,
  generateQRFilename,
  validateQROptions,
  getQRColorSchemes,
  saveQRStyle,
  type QRCodeResult,
  type SaveQRStyleOptions
} from "@/lib/qr-generator";
import { getRecentQRStyles, deleteQRStyle } from "@/lib/firebase/firestore";
import { QRStyle, QRStyleCategory } from "@/types/models";

export default function QRGeneratorPage() {
  const { t, isClient } = useLocale();
  const { user } = useAuth();

  // State for form inputs
  const [qrType, setQrType] = useState('menu');
  const [size, setSize] = useState(300);
  const [contentUrl, setContentUrl] = useState('');
  const [foregroundColor, setForegroundColor] = useState('#56999B');
  const [backgroundColor, setBackgroundColor] = useState('#FFFFFF');

  // State for QR generation
  const [qrResult, setQrResult] = useState<QRCodeResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStyleId, setCurrentStyleId] = useState<string | null>(null);

  // State for style management
  const [recentStyles, setRecentStyles] = useState<QRStyle[]>([]);
  const [isLoadingStyles, setIsLoadingStyles] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [styleName, setStyleName] = useState('');
  const [styleDescription, setStyleDescription] = useState('');

  // State for delete confirmation
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [styleToDelete, setStyleToDelete] = useState<QRStyle | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Auto-populate URL with menu URL and load recent styles
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const baseURL = window.location.origin;
      setContentUrl(`${baseURL}/menu`);
    }

    // Load recent styles if user is authenticated
    if (user?.uid) {
      loadRecentStyles();
    }
  }, [user]);

  // Load recent QR styles
  const loadRecentStyles = async () => {
    if (!user?.uid) return;

    setIsLoadingStyles(true);
    try {
      console.log('Loading recent styles for user:', user.uid);
      const styles = await getRecentQRStyles(user.uid, 4);
      console.log('Loaded recent styles:', styles);
      setRecentStyles(styles);
    } catch (error) {
      console.error('Error loading recent styles:', error);
    } finally {
      setIsLoadingStyles(false);
    }
  };

  // Real-time preview - generate QR code when colors or size change
  useEffect(() => {
    const generatePreview = async () => {
      if (contentUrl && typeof window !== 'undefined') {
        try {
          const baseURL = window.location.origin;
          const result = await generateMenuQRCode(baseURL, {
            size: Math.min(size, 300), // Limit preview size
            foregroundColor,
            backgroundColor
          });
          setQrResult(result);
        } catch (err) {
          console.error('Preview generation error:', err);
          // Don't show error for preview, just keep previous result
        }
      }
    };

    // Debounce the preview generation
    const timeoutId = setTimeout(generatePreview, 500);
    return () => clearTimeout(timeoutId);
  }, [contentUrl, foregroundColor, backgroundColor, size]);

  // Generate QR code (for manual generation with full size)
  const handleGenerateQR = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      // Validate options
      const validation = validateQROptions({
        size,
        foregroundColor,
        backgroundColor
      });

      if (!validation.isValid) {
        setError(validation.errors.join(', '));
        return;
      }

      // Generate QR code with full size
      const baseURL = window.location.origin;
      const result = await generateMenuQRCode(baseURL, {
        size,
        foregroundColor,
        backgroundColor
      });

      setQrResult(result);
      setError(null);
    } catch (err) {
      setError('Failed to generate QR code. Please try again.');
      console.error('QR generation error:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle save style
  const handleSaveStyle = async () => {
    if (!qrResult || !user?.uid || !styleName.trim()) return;

    try {
      console.log('Saving QR style:', { name: styleName, userId: user.uid });

      const saveOptions: SaveQRStyleOptions = {
        userId: user.uid,
        name: styleName.trim(),
        description: styleDescription.trim() || undefined,
        category: QRStyleCategory.CUSTOM,
        tags: ['custom']
      };

      const savedStyle = await saveQRStyle(
        qrResult,
        { size, foregroundColor, backgroundColor },
        saveOptions,
        contentUrl
      );

      console.log('QR style saved successfully:', savedStyle);

      setCurrentStyleId(savedStyle.id);
      setShowSaveDialog(false);
      setStyleName('');
      setStyleDescription('');

      // Reload recent styles
      console.log('Reloading recent styles...');
      await loadRecentStyles();
    } catch (error) {
      console.error('Error saving style:', error);
      setError('Failed to save QR style. Please try again.');
    }
  };

  // Handle download PNG
  const handleDownloadPNG = async () => {
    if (qrResult) {
      const filename = generateQRFilename(foregroundColor, backgroundColor);
      await downloadQRCodePNG(qrResult.dataURL, filename, currentStyleId || undefined);
    }
  };

  // Handle download SVG
  const handleDownloadSVG = async () => {
    if (qrResult) {
      const filename = generateQRFilename(foregroundColor, backgroundColor);
      await downloadQRCodeSVG(qrResult.svg, filename, currentStyleId || undefined);
    }
  };

  // Handle print
  const handlePrint = async () => {
    if (qrResult) {
      await printQRCode(qrResult.dataURL, 'BarcodeCafe Menu QR Code', currentStyleId || undefined);
    }
  };

  // Handle apply style from recent styles
  const handleApplyStyle = (style: QRStyle) => {
    setSize(style.size);
    setForegroundColor(style.foregroundColor);
    setBackgroundColor(style.backgroundColor);
    setCurrentStyleId(style.id);
  };

  // Handle delete style confirmation
  const handleDeleteClick = (style: QRStyle, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent applying the style when clicking delete
    setStyleToDelete(style);
    setShowDeleteDialog(true);
  };

  // Handle delete style
  const handleDeleteStyle = async () => {
    if (!styleToDelete) return;

    setIsDeleting(true);
    try {
      await deleteQRStyle(styleToDelete.id);

      // Remove from local state
      setRecentStyles(prev => prev.filter(style => style.id !== styleToDelete.id));

      // Clear current style if it was the deleted one
      if (currentStyleId === styleToDelete.id) {
        setCurrentStyleId(null);
      }

      setShowDeleteDialog(false);
      setStyleToDelete(null);
    } catch (error) {
      console.error('Error deleting style:', error);
      setError('Failed to delete QR style. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle cancel delete
  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
    setStyleToDelete(null);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {isClient ? t('admin.qrGeneratorMenu') : 'QR Generator'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {isClient ? t('admin.qrGeneratorDescription') : 'Generate QR codes for your cafe menu and tables'}
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* QR Code Generator Form */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">
              {isClient ? t('admin.qrGenerator.generateNew') : 'Generate New QR Code'}
            </h3>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.codeType') : 'QR Code Type'}
                  </label>
                  <select
                    value={qrType}
                    onChange={(e) => setQrType(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  >
                    <option value="menu">Customer Menu</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.size') : 'Size (px)'}
                  </label>
                  <input
                    type="number"
                    value={size}
                    onChange={(e) => setSize(parseInt(e.target.value) || 300)}
                    min="100"
                    max="2000"
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isClient ? t('admin.qrGenerator.contentUrl') : 'Content/URL'}
                </label>
                <input
                  type="text"
                  value={contentUrl}
                  onChange={(e) => setContentUrl(e.target.value)}
                  placeholder={isClient ? t('admin.qrGenerator.urlPlaceholder') : 'Enter URL or content for QR code'}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  readOnly
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {isClient ? t('admin.qrGenerator.menuRedirectNote') : 'QR code will redirect customers to your menu page'}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.foregroundColor') : 'Foreground Color'}
                  </label>
                  <input
                    type="color"
                    value={foregroundColor}
                    onChange={(e) => setForegroundColor(e.target.value)}
                    className="w-full h-10 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.backgroundColor') : 'Background Color'}
                  </label>
                  <input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-full h-10 rounded-lg"
                  />
                </div>
              </div>

              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
                </div>
              )}

              <button
                onClick={handleGenerateQR}
                disabled={isGenerating}
                className="w-full bg-[#74C8CA] hover:bg-[#56999B] dark:bg-[#5DBDC0] dark:hover:bg-[#56999B] disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 rounded-lg transition-colors"
              >
                {isGenerating ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </span>
                ) : (
                  isClient ? t('admin.qrGenerator.generateButton') : 'Generate QR Code'
                )}
              </button>
            </div>
          </div>
        </div>

        {/* QR Code Preview */}
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">
            {isClient ? t('admin.qrGenerator.preview') : 'Preview'}
          </h3>
          <div className="flex flex-col items-center space-y-6">
            <div className="w-48 h-48 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
              {qrResult ? (
                <img
                  src={qrResult.dataURL}
                  alt="Generated QR Code"
                  className="w-full h-full object-contain rounded-lg"
                />
              ) : (
                <i className="fa-solid fa-qrcode text-6xl text-[#74C8CA] dark:text-[#5DBDC0]"></i>
              )}
            </div>
            <div className="flex flex-wrap gap-2 justify-center">
              <button
                onClick={() => setShowSaveDialog(true)}
                disabled={!qrResult || !user}
                className="px-3 py-2 bg-[#74C8CA] hover:bg-[#56999B] dark:bg-[#5DBDC0] dark:hover:bg-[#56999B] disabled:opacity-50 disabled:cursor-not-allowed text-white text-sm rounded-lg transition-colors"
              >
                <i className="fa-solid fa-save mr-2"></i>
                {isClient ? t('admin.qrGenerator.saveStyle') : 'Save Style'}
              </button>
              <button
                onClick={handleDownloadPNG}
                disabled={!qrResult}
                className="px-3 py-2 bg-[#A8FDFF] dark:bg-[#5DBDC0]/30 text-[#56999B] dark:text-[#5DBDC0] rounded-lg hover:bg-[#83EAED] dark:hover:bg-[#5DBDC0]/40 disabled:opacity-50 disabled:cursor-not-allowed text-sm transition-colors"
              >
                <i className="fa-solid fa-download mr-1"></i>
                PNG
              </button>
              <button
                onClick={handleDownloadSVG}
                disabled={!qrResult}
                className="px-3 py-2 bg-[#A8FDFF] dark:bg-[#5DBDC0]/30 text-[#56999B] dark:text-[#5DBDC0] rounded-lg hover:bg-[#83EAED] dark:hover:bg-[#5DBDC0]/40 disabled:opacity-50 disabled:cursor-not-allowed text-sm transition-colors"
              >
                <i className="fa-solid fa-download mr-1"></i>
                SVG
              </button>
              <button
                onClick={handlePrint}
                disabled={!qrResult}
                className="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm transition-colors"
              >
                <i className="fa-solid fa-print mr-1"></i>
                {isClient ? t('admin.qrGenerator.print') : 'Print'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Recent QR Codes */}
      <div className="mt-8">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">
          {isClient ? t('admin.qrGenerator.recentCodes') : 'Recently Generated QR Codes'}
        </h3>
        {isLoadingStyles ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-4 animate-pulse">
                <div className="w-full aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : recentStyles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {recentStyles.map((style) => (
              <div
                key={style.id}
                className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-4 cursor-pointer hover:shadow-md transition-shadow relative group"
                onClick={() => handleApplyStyle(style)}
              >
                {/* Delete Button */}
                <button
                  onClick={(e) => handleDeleteClick(style, e)}
                  className="absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  title="Delete QR Style"
                >
                  <i className="fa-solid fa-trash text-xs"></i>
                </button>

                <div className="w-full aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mb-4 overflow-hidden">
                  {style.qrDataURL ? (
                    <img
                      src={style.qrDataURL}
                      alt={`QR Code - ${style.name}`}
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <i className="fa-solid fa-qrcode text-4xl text-[#74C8CA] dark:text-[#5DBDC0]"></i>
                  )}
                </div>
                <div className="text-sm">
                  <p className="font-medium text-gray-800 dark:text-white truncate" title={style.name}>
                    {style.name}
                  </p>
                  <div className="flex items-center justify-between mt-2 text-xs text-gray-400">
                    <span className="flex items-center">
                      <i className="fa-solid fa-download mr-1"></i>
                      {style.downloadCount}
                    </span>
                    <span className="flex items-center">
                      <i className="fa-solid fa-print mr-1"></i>
                      {style.printCount}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <i className="fa-solid fa-qrcode text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
            <p className="text-gray-500 dark:text-gray-400">
              No QR codes generated yet. Create your first QR code above!
            </p>
          </div>
        )}
      </div>

      {/* Save Style Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
          <div className="bg-white dark:bg-[#1d2127] rounded-xl p-6 w-full max-w-md shadow-xl border border-gray-200 dark:border-gray-700 relative z-10">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              {isClient ? t('admin.qrGenerator.saveQRStyle') : 'Save QR Style'}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isClient ? t('admin.qrGenerator.styleNameLabel') : 'Style Name'} *
                </label>
                <input
                  type="text"
                  value={styleName}
                  onChange={(e) => setStyleName(e.target.value)}
                  placeholder={isClient ? t('admin.qrGenerator.styleNamePlaceholder') : 'Enter a name for this QR style'}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isClient ? t('admin.qrGenerator.descriptionLabel') : 'Description (Optional)'}
                </label>
                <textarea
                  value={styleDescription}
                  onChange={(e) => setStyleDescription(e.target.value)}
                  placeholder={isClient ? t('admin.qrGenerator.descriptionPlaceholder') : 'Describe this QR style...'}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowSaveDialog(false);
                  setStyleName('');
                  setStyleDescription('');
                }}
                className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                {isClient ? t('admin.qrGenerator.cancel') : 'Cancel'}
              </button>
              <button
                onClick={handleSaveStyle}
                disabled={!styleName.trim()}
                className="flex-1 px-4 py-2 bg-[#74C8CA] hover:bg-[#56999B] dark:bg-[#5DBDC0] dark:hover:bg-[#56999B] disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {isClient ? t('admin.qrGenerator.saveStyleButton') : 'Save Style'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && styleToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
          <div className="bg-white dark:bg-[#1d2127] rounded-xl p-6 w-full max-w-md shadow-xl border border-gray-200 dark:border-gray-700 relative z-10">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mr-3">
                <i className="fa-solid fa-trash text-red-600 dark:text-red-400"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                {isClient ? t('admin.qrGenerator.deleteStyle') : 'Delete QR Style'}
              </h3>
            </div>

            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {isClient
                ? t('admin.qrGenerator.deleteConfirmMessage', { styleName: styleToDelete.name })
                : `Are you sure you want to delete the QR style "${styleToDelete.name}"? This action cannot be undone.`
              }
            </p>

            <div className="flex space-x-3">
              <button
                onClick={handleCancelDelete}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isClient ? t('admin.qrGenerator.cancel') : 'Cancel'}
              </button>
              <button
                onClick={handleDeleteStyle}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {isDeleting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isClient ? t('admin.qrGenerator.deleting') : 'Deleting...'}
                  </span>
                ) : (
                  isClient ? t('admin.qrGenerator.delete') : 'Delete'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}