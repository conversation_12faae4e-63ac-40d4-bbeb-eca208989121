"use client";

import { useState, useEffect, useRef } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { collection, getDocs, query, where, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase/config";
import { Category, MenuItem, StockStatus, Offer } from "@/types/models";
import { getActivePackageDeals, getPackageMenuItems } from "@/lib/firebase/firestore";
import Link from "next/link";
import LanguageSwitcher from "@/components/ui/LanguageSwitcher";
import { Sheet, SheetContent, SheetTitle } from "@/components/ui/sheet";
import ItemQuantitySelector from "@/components/menu/ItemQuantitySelector";
import CartButton from "@/components/menu/CartButton";
import PackageCard from "@/components/menu/PackageCard";
import PackageDetailModal, { PackageCustomization } from "@/components/menu/PackageDetailModal";
import { useCart } from "@/contexts/CartContext";
import { toast } from "@/hooks/use-toast";

export default function MenuPage() {
  const { t, isClient, locale } = useLocale();
  const isRTL = locale === 'ar';
  const { addToCart } = useCart();
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [menuItems, setMenuItems] = useState<Record<string, MenuItem[]>>({});
  const [packageDeals, setPackageDeals] = useState<Offer[]>([]);
  const [packageMenuItems, setPackageMenuItems] = useState<Record<string, MenuItem[]>>({});
  const [allMenuItems, setAllMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const categoriesContainerRef = useRef<HTMLDivElement>(null);
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [isDetailSheetOpen, setIsDetailSheetOpen] = useState(false);
  const [selectedQuantity, setSelectedQuantity] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<MenuItem[]>([]);
  // Package deal modal state
  const [selectedPackage, setSelectedPackage] = useState<Offer | null>(null);
  const [selectedPackageItems, setSelectedPackageItems] = useState<MenuItem[]>([]);
  const [isPackageModalOpen, setIsPackageModalOpen] = useState(false);

  // Handle search functionality
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value.toLowerCase();
    setSearchQuery(query);

    if (query.trim() === "") {
      setSearchResults([]);
      return;
    }

    // Search across all menu items (including Arabic fields)
    const results: MenuItem[] = [];
    Object.values(menuItems).forEach(categoryItems => {
      const matchingItems = categoryItems.filter(item =>
        item.title.toLowerCase().includes(query) ||
        (item.title_ar && item.title_ar.toLowerCase().includes(query)) ||
        (item.description && item.description.toLowerCase().includes(query)) ||
        (item.description_ar && item.description_ar.toLowerCase().includes(query)) ||
        (item.ingredients && item.ingredients.toLowerCase().includes(query)) ||
        (item.ingredients_ar && item.ingredients_ar.toLowerCase().includes(query)) ||
        (item.allergens && item.allergens.toLowerCase().includes(query)) ||
        (item.allergens_ar && item.allergens_ar.toLowerCase().includes(query))
      );
      results.push(...matchingItems);
    });

    setSearchResults(results);
  };

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        // Get categories that are active and visible
        const categoriesQuery = query(
          collection(db, "categories"),
          where("isActive", "==", true),
          where("isVisible", "==", true),
          orderBy("displayOrder", "asc")
        );
        
        const categoriesSnapshot = await getDocs(categoriesQuery);
        const fetchedCategories: Category[] = [];
        
        categoriesSnapshot.forEach((doc) => {
          const data = doc.data() as Omit<Category, "id">;
          fetchedCategories.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt instanceof Date ? data.createdAt : new Date(data.createdAt as string),
            updatedAt: data.updatedAt instanceof Date ? data.updatedAt : new Date(data.updatedAt as string),
          });
        });
        
        setCategories(fetchedCategories);
        
        // Set the active category to the first one if available
        if (fetchedCategories.length > 0) {
          setActiveCategory(fetchedCategories[0].id);
        }
        
        // Fetch menu items for each category
        const itemsByCategory: Record<string, MenuItem[]> = {};
        
        for (const category of fetchedCategories) {
          const menuItemsQuery = query(
            collection(db, "menuItems"),
            where("categoryId", "==", category.id),
            where("isActive", "==", true),
            orderBy("isFeatured", "desc"),
            orderBy("price", "asc")
          );
          
          const menuItemsSnapshot = await getDocs(menuItemsQuery);
          const categoryItems: MenuItem[] = [];
          
          menuItemsSnapshot.forEach((doc) => {
            const data = doc.data() as Omit<MenuItem, "id">;
            categoryItems.push({
              id: doc.id,
              ...data,
              createdAt: data.createdAt instanceof Date ? data.createdAt : new Date(data.createdAt as string),
              updatedAt: data.updatedAt instanceof Date ? data.updatedAt : new Date(data.updatedAt as string),
            });
          });
          
          itemsByCategory[category.id] = categoryItems;
        }
        
        setMenuItems(itemsByCategory);

        // Collect all menu items for package deals
        const allItems: MenuItem[] = [];
        Object.values(itemsByCategory).forEach(categoryItems => {
          allItems.push(...categoryItems);
        });
        setAllMenuItems(allItems);

        // Fetch package deals
        console.log('Fetching package deals...');
        const packageDealsData = await getActivePackageDeals();
        console.log('Package deals fetched:', packageDealsData);
        setPackageDeals(packageDealsData);

        // Fetch menu items for each package deal
        const packageItemsData: Record<string, MenuItem[]> = {};
        for (const packageDeal of packageDealsData) {
          if (packageDeal.conditions.applicableMenuItems && packageDeal.conditions.applicableMenuItems.length > 0) {
            console.log('Fetching items for package:', packageDeal.name, 'with IDs:', packageDeal.conditions.applicableMenuItems);
            // Convert applicableMenuItems (string[]) to PackageItem[] format for the function
            const packageItems = packageDeal.conditions.applicableMenuItems.map(itemId => ({
              menuItemId: itemId,
              quantity: 1,
              isOptional: false
            }));
            const items = await getPackageMenuItems(packageItems);
            console.log('Package items fetched:', items);
            packageItemsData[packageDeal.id] = items;
          }
        }
        setPackageMenuItems(packageItemsData);

      } catch (err) {
        console.error("Error fetching menu data:", err);
        setError("Failed to load menu. Please try again later.");
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, []);
  
  const getStockStatusLabel = (status: StockStatus): string => {
    switch (status) {
      case StockStatus.IN_STOCK:
        return isClient ? t('menu.stockStatus.inStock') : 'In Stock';
      case StockStatus.LOW_STOCK:
        return isClient ? t('menu.stockStatus.lowStock') : 'Low Stock';
      case StockStatus.OUT_OF_STOCK:
        return isClient ? t('menu.stockStatus.outOfStock') : 'Out of Stock';
      default:
        return 'Unknown';
    }
  };
  
  const getStockStatusClass = (status: StockStatus): string => {
    switch (status) {
      case StockStatus.IN_STOCK:
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400';
      case StockStatus.LOW_STOCK:
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400';
      case StockStatus.OUT_OF_STOCK:
        return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400';
    }
  };

  const scrollCategory = (direction: 'left' | 'right') => {
    if (categoriesContainerRef.current) {
      const scrollAmount = 200;
      const container = categoriesContainerRef.current;
      
      if (direction === 'left') {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
    }
  };

  const showItemDetails = (item: MenuItem) => {
    setSelectedItem(item);
    setSelectedQuantity(1); // Reset quantity when opening a new item
    setIsDetailSheetOpen(true);
  };

  const closeItemDetails = () => {
    setIsDetailSheetOpen(false);
  };

  const handleAddToCart = () => {
    if (!selectedItem) return;

    // Add selected item to cart with the chosen quantity
    addToCart(selectedItem, selectedQuantity);

    // Show success message
    toast({
      title: t('menu.addedToCart'),
      description: `${selectedItem.title} (×${selectedQuantity})`,
      variant: 'success',
    });

    // Close the detail sheet
    setIsDetailSheetOpen(false);
  };

  // Package deal handlers
  const handleViewPackageDetails = (offer: Offer, packageItems: MenuItem[]) => {
    console.log('handleViewPackageDetails called', {
      offer: {
        id: offer.id,
        name: offer.name,
        conditions: offer.conditions
      },
      packageItems: packageItems.map(item => ({ id: item.id, title: item.title })),
      packageItemsLength: packageItems.length
    });
    setSelectedPackage(offer);
    setSelectedPackageItems(packageItems);
    setIsPackageModalOpen(true);
    console.log('Modal state set to true');
  };

  const handleAddPackageToCart = (offer: Offer, packageItems: MenuItem[], customizations?: PackageCustomization[]) => {
    // For now, we'll add the package as individual items
    // In a more advanced implementation, we could create a special package cart item type
    packageItems.forEach(item => {
      // Default quantity is 1 for each item in the package
      const quantity = 1;

      // Check if item is included (for optional items)
      const customization = customizations?.find(c => c.originalItemId === item.id);
      if (customization && !customization.isIncluded) {
        return; // Skip optional items that are not included
      }

      addToCart(item, quantity);
    });

    // Show success message
    toast({
      title: isClient ? t('packages.addedToCart') : 'Package added to cart!',
      description: `${locale === 'ar' && offer.name_ar ? offer.name_ar : offer.name}`,
      variant: 'success',
    });
  };

  return (
    <div className="min-h-screen" dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header with Brand Logo and Hero Image - Enhanced Design */}
      <header className="relative text-center overflow-hidden">
        {/* Hero Background Image */}
        <div className="absolute inset-0 w-full h-full">
          <img 
            src="/menu-header-01.jpg" 
            alt="Coffee beans background" 
            className="w-full h-full object-cover" 
          />
          <div className="absolute inset-0 bg-gradient-to-b from-[#000000]/70 to-[#000000]/40"></div>
        </div>
        
        {/* Header Content */}
        <div className="relative z-10 py-8 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center">
              <div className="flex-1 flex justify-start">
                <Link href="/" className="flex items-center gap-3">
                  <div className="w-28 h-28">
                    <img src="/logo-white.svg" alt="Barcode Logo" className="w-full h-full" />
                  </div>
                </Link>
              </div>
              <div className="flex-1 flex justify-end">
                <div className="flex items-center gap-3">
                  {/* Search Bar */}
                  <div className="relative w-48 md:w-64 mr-2">
                    <input
                      type="text"
                      placeholder={isClient ? t('common.search') : 'Search menu...'}
                      value={searchQuery}
                      onChange={handleSearch}
                      className="w-full py-2 px-4 pr-10 rounded-full bg-white/20 backdrop-blur-sm text-white placeholder-white/70 border border-white/30 focus:outline-none focus:ring-2 focus:ring-[#d27d46] text-sm"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <i className="fa-solid fa-search text-white/70"></i>
                    </div>
                  </div>
                  
                  <div className="custom-language-switcher">
                    <LanguageSwitcher />
                  </div>
                  <Link 
                    href="/customer/dashboard" 
                    aria-label={isClient ? t('customer.profile') : 'Customer Profile'}
                    title={isClient ? t('customer.profile') : 'Customer Profile'}
                  >
                    <div className="w-10 h-10 rounded-full bg-white/90 dark:bg-[#2c3436]/90 flex items-center justify-center cursor-pointer hover:opacity-90 transition-opacity shadow-md">
                      <i className="fa-solid fa-user text-[#c27845] dark:text-[#d27d46]"></i>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
            
            <div className="mt-12 mb-6">
              <h1 className="text-4xl font-bold text-white mb-4">
                {isClient ? t('common.cafeTagline') : 'Barcode Speciality Coffee'}
              </h1>
              <p className="text-[#e09a62] max-w-lg mx-auto text-lg">
                {isClient ? t('common.cafeDescription') : 'Experience our handcrafted specialty coffee and delicious menu items.'}
              </p>
            </div>
            
            {/* Search Results Display */}
            {searchQuery.trim() !== "" && searchResults.length > 0 && (
              <div className="bg-white/10 backdrop-blur-md rounded-lg p-4 mb-6 max-h-60 overflow-y-auto">
                <h3 className="text-white font-medium mb-2">
                  {isClient ? t('menu.searchResults') : 'Search Results'} ({searchResults.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {searchResults.map((item) => (
                    <div 
                      key={item.id} 
                      className="bg-white/10 hover:bg-white/20 rounded-lg p-3 cursor-pointer transition-colors"
                      onClick={() => showItemDetails(item)}
                    >
                      <div className="flex items-center gap-2">
                        {item.image && (
                          <div className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0">
                            <img src={item.image} alt={item.title} className="w-full h-full object-cover" />
                          </div>
                        )}
                        <div>
                          <div className="text-white font-medium">
                            {locale === 'ar' && item.title_ar ? item.title_ar : item.title}
                          </div>
                          <div className="text-[#e09a62] text-sm">{item.price.toFixed(2)} {isClient ? t('common.currency') : 'KWD'}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {searchQuery.trim() !== "" && searchResults.length === 0 && (
              <div className="bg-white/10 backdrop-blur-md rounded-lg p-4 mb-6 text-center">
                <p className="text-white">
                  {isClient ? t('menu.noSearchResults') : 'No items found matching your search.'}
                </p>
              </div>
            )}
          </div>
        </div>
      </header>
      
      {/* Categories Navigation - New Design */}
      <div className="sticky top-0 bg-white dark:bg-[#2c3436] shadow-md z-20">
        <div className="max-w-7xl mx-auto relative overflow-hidden">
          <div className="absolute left-0 top-0 bottom-0 flex items-center px-1 bg-gradient-to-r from-white dark:from-[#2c3436] z-10">
            <button 
              onClick={() => scrollCategory('left')}
              className="text-[#703f23] dark:text-[#e09a62] p-2 hover:bg-[#dac6ae]/20 dark:hover:bg-[#392e23]/30 rounded-full"
              aria-label="Scroll categories left"
            >
              <i className="fa-solid fa-chevron-left"></i>
            </button>
          </div>
          
          <div 
            ref={categoriesContainerRef}
            className="flex overflow-x-auto py-3 px-10 hide-scrollbar items-stretch"
            style={{ scrollBehavior: 'smooth' }}
          >
            {categories.map((category) => (
              <div 
                key={category.id}
                className="flex-shrink-0 mx-4 first:ml-0 last:mr-0"
              >
                <button
                  onClick={() => setActiveCategory(category.id)}
                  className={`relative px-4 py-2 rounded-full transition-all ${
                    activeCategory === category.id 
                      ? 'bg-[#c27845] dark:bg-[#d27d46] text-white font-medium' 
                      : 'text-[#703f23] dark:text-[#e09a62] hover:bg-[#dac6ae]/20 dark:hover:bg-[#392e23]/30'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <i className={`fa-solid ${category.icon}`}></i>
                    <span>{locale === 'ar' && category.name_ar ? category.name_ar : category.name}</span>
                  </div>
                </button>
              </div>
            ))}
          </div>
          
          <div className="absolute right-0 top-0 bottom-0 flex items-center px-1 bg-gradient-to-l from-white dark:from-[#2c3436] z-10">
            <button 
              onClick={() => scrollCategory('right')}
              className="text-[#703f23] dark:text-[#e09a62] p-2 hover:bg-[#dac6ae]/20 dark:hover:bg-[#392e23]/30 rounded-full"
              aria-label="Scroll categories right"
            >
              <i className="fa-solid fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content - New Design */}
      <main className="w-full bg-white dark:bg-[#2c3436] py-6 min-h-screen">
        <div className="max-w-7xl mx-auto px-4">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#c27845] dark:border-[#d27d46]"></div>
            </div>
          ) : error ? (
            <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-6 rounded-xl text-center mx-auto max-w-md">
              <p>{error}</p>
            </div>
          ) : (
            <div className="flex flex-col">
              {/* Package Deals Section */}
              {packageDeals.length > 0 && (
                <div className="mb-8">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="flex items-center gap-2">
                      <i className="fa-solid fa-box-open text-green-600 text-xl"></i>
                      <h2 className="font-bold text-2xl text-[#703f23] dark:text-[#e09a62]">
                        {isClient ? t('packages.packageDeals') : 'Package Deals'}
                      </h2>
                    </div>
                    <div className="flex-1 h-px bg-gradient-to-r from-green-200 to-transparent dark:from-green-800"></div>
                  </div>
                  <p className="text-[#94795e] dark:text-[#b49678] text-sm mb-6">
                    {isClient ? t('packages.specialOffers') : 'Special package offers with great savings'}
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {packageDeals.map((packageDeal) => {
                      const items = packageMenuItems[packageDeal.id] || [];
                      return (
                        <PackageCard
                          key={packageDeal.id}
                          offer={packageDeal}
                          packageItems={items}
                          onViewDetails={handleViewPackageDetails}
                          onAddToCart={handleAddPackageToCart}
                        />
                      );
                    })}
                  </div>
                </div>
              )}



              {/* Category Description Section */}
              <div className="mb-6">
                <h2 className="font-bold text-2xl text-[#703f23] dark:text-[#e09a62] mb-2">
                  {activeCategory && (() => {
                    const category = categories.find(c => c.id === activeCategory);
                    return locale === 'ar' && category?.name_ar ? category.name_ar : category?.name;
                  })()}
                </h2>
                <p className="text-[#94795e] dark:text-[#b49678] text-sm">
                  {activeCategory && (() => {
                    const category = categories.find(c => c.id === activeCategory);
                    const description = locale === 'ar' && category?.description_ar ? category.description_ar : category?.description;
                    return description || (isClient ? t('menu.categoryDescription') : 'Explore our delicious offerings in this category.');
                  })()}
                </p>
              </div>
              
              {/* Menu Items - Grid Layout */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {activeCategory && menuItems[activeCategory]?.length === 0 ? (
                  <div className="bg-white dark:bg-[#392e23] rounded-xl shadow-sm p-8 text-center col-span-full">
                    <p className="text-[#703f23] dark:text-[#e09a62]">
                      {isClient ? t('menu.noItems') : 'No menu items found in this category.'}
                    </p>
                  </div>
                ) : (
                  activeCategory && menuItems[activeCategory]?.map((item: MenuItem) => (
                    <div 
                      key={item.id} 
                      className="bg-white dark:bg-[#392e23] rounded-xl shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-all hover:translate-y-[-2px]"
                      onClick={() => showItemDetails(item)}
                    >
                      {item.image && (
                        <div className="w-full h-48 relative">
                          <img 
                            src={item.image} 
                            alt={item.title} 
                            className="w-full h-full object-cover"
                          />
                          {item.isFeatured && (
                            <div className="absolute top-2 right-2 bg-[#c27845] dark:bg-[#d27d46] text-white text-xs px-2 py-1 rounded-full">
                              {isClient ? t('menu.featured') : 'Featured'}
                            </div>
                          )}
                        </div>
                      )}
                      <div className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-bold text-[#703f23] dark:text-[#e09a62]">
                            {locale === 'ar' && item.title_ar ? item.title_ar : item.title}
                          </h3>
                          <span className="font-semibold text-[#c27845] dark:text-[#d27d46] ml-2">
                            {isClient ? `${t('common.currency')} ${item.price.toFixed(2)}` : `SAR ${item.price.toFixed(2)}`}
                          </span>
                        </div>

                        <p className="text-sm text-[#94795e] dark:text-[#b49678] line-clamp-2 mb-3">
                          {locale === 'ar' && item.description_ar ? item.description_ar : item.description}
                        </p>
                        
                        <div className="flex justify-between items-center">
                          <div className="flex flex-wrap gap-2">
                            {item.prepTime && (
                              <div className="flex items-center text-xs text-[#94795e] dark:text-[#b49678]">
                                <i className="fa-regular fa-clock mr-1"></i>
                                <span>{item.prepTime} {isClient ? t('common.min') : 'min'}</span>
                              </div>
                            )}
                            {/* Display actual caffeine data from MenuItem */}
                            {(item.caffeine || item.caffeine_ar) && (
                              <div className="flex items-center text-xs text-[#94795e] dark:text-[#b49678]">
                                <i className="fa-solid fa-bolt text-yellow-500 mr-1"></i>
                                <span>{locale === 'ar' && item.caffeine_ar ? item.caffeine_ar : item.caffeine}</span>
                              </div>
                            )}
                            {/* Delivery availability indicator */}
                            <div className={`flex items-center text-xs ${item.isAvailableForDelivery ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400'}`}>
                              <i className={`fa-solid ${item.isAvailableForDelivery ? 'fa-truck' : 'fa-store'} mr-1`}></i>
                              <span>{item.isAvailableForDelivery ? (isClient ? t('menu.availableForDelivery') : 'Delivery') : (isClient ? t('menu.pickupOnly') : 'Pickup only')}</span>
                            </div>
                          </div>
                          
                          {item.stockStatus !== StockStatus.IN_STOCK ? (
                            <span className={`px-2 py-1 ${getStockStatusClass(item.stockStatus)} rounded-full text-xs ml-auto`}>
                              {getStockStatusLabel(item.stockStatus)}
                            </span>
                          ) : (
                            <button className="ml-auto text-sm bg-[#c27845] dark:bg-[#d27d46] text-white px-3 py-1 rounded-full hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-colors">
                              <i className="fa-solid fa-plus mr-1"></i>
                              {isClient ? t('menu.add') : 'Add'}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Item Detail Sheet */}
      <Sheet open={isDetailSheetOpen} onOpenChange={setIsDetailSheetOpen}>
        <SheetContent className="bg-white dark:bg-[#392e23] overflow-y-auto p-0">
          <SheetTitle className="sr-only">{selectedItem ? selectedItem.title : 'Menu Item Details'}</SheetTitle>
          {selectedItem && (
            <div className="flex flex-col h-full">
              {/* Item Image with Close Button */}
              <div className="relative w-full h-64">
                {selectedItem.image ? (
                  <img 
                    src={selectedItem.image} 
                    alt={selectedItem.title} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-[#f8f5e9] dark:bg-[#2c3436] flex items-center justify-center">
                    <i className="fa-solid fa-utensils text-4xl text-[#c27845] dark:text-[#d27d46]"></i>
                  </div>
                )}
                
                {/* Featured Badge */}
                {selectedItem.isFeatured && (
                  <div className="absolute top-4 right-4 bg-[#c27845] dark:bg-[#d27d46] text-white text-xs px-3 py-1 rounded-full">
                    {isClient ? t('menu.featured') : 'Featured'}
                  </div>
                )}
              </div>
              
              {/* Content Container */}
              <div className="flex-1 p-6">
                {/* Title and Price */}
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-[#703f23] dark:text-[#e09a62] text-xl font-bold">
                    {locale === 'ar' && selectedItem.title_ar ? selectedItem.title_ar : selectedItem.title}
                  </h2>
                  <span className="text-[#c27845] dark:text-[#d27d46] font-bold text-lg">
                    {isClient ? `${t('common.currency')} ${selectedItem.price.toFixed(2)}` : `SAR ${selectedItem.price.toFixed(2)}`}
                  </span>
                </div>
                
                {/* Stock Status */}
                {selectedItem.stockStatus !== StockStatus.IN_STOCK && (
                  <div className="mb-4">
                    <span className={`inline-block px-3 py-1 ${getStockStatusClass(selectedItem.stockStatus)} rounded-full text-sm`}>
                      {getStockStatusLabel(selectedItem.stockStatus)}
                    </span>
                  </div>
                )}
                
                {/* Description */}
                {(selectedItem.description || selectedItem.description_ar) && (
                  <div className="mb-6">
                    <h3 className="text-[#703f23] dark:text-[#e09a62] font-medium mb-2">
                      {isClient ? t('menu.description') : 'Description'}
                    </h3>
                    <p className="text-[#94795e] dark:text-[#b49678] text-sm leading-relaxed">
                      {locale === 'ar' && selectedItem.description_ar ? selectedItem.description_ar : selectedItem.description}
                    </p>
                  </div>
                )}
                
                {/* Quick Info */}
                <div className="flex flex-wrap gap-4 mb-6 text-sm text-[#94795e] dark:text-[#b49678]">
                  {selectedItem.prepTime && (
                    <div className="flex items-center">
                      <i className="fa-regular fa-clock mr-2"></i>
                      <span>{selectedItem.prepTime} {isClient ? t('common.min') : 'min'}</span>
                    </div>
                  )}
                  {(selectedItem.caffeine || selectedItem.caffeine_ar) && (
                    <div className="flex items-center">
                      <i className="fa-solid fa-bolt text-yellow-500 mr-2"></i>
                      <span>{locale === 'ar' && selectedItem.caffeine_ar ? selectedItem.caffeine_ar : selectedItem.caffeine}</span>
                    </div>
                  )}
                  {/* Delivery availability */}
                  <div className={`flex items-center ${selectedItem.isAvailableForDelivery ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400'}`}>
                    <i className={`fa-solid ${selectedItem.isAvailableForDelivery ? 'fa-truck' : 'fa-store'} mr-2`}></i>
                    <span>{selectedItem.isAvailableForDelivery ? (isClient ? t('menu.availableForDelivery') : 'Available for delivery') : (isClient ? t('menu.pickupOnly') : 'Pickup/table service only')}</span>
                  </div>
                </div>
                
                {/* Ingredients */}
                {(selectedItem.ingredients || selectedItem.ingredients_ar) && (
                  <div className="mb-6">
                    <h3 className="text-[#703f23] dark:text-[#e09a62] font-medium mb-2">
                      {isClient ? t('menu.ingredients') : 'Ingredients'}
                    </h3>
                    <p className="text-[#94795e] dark:text-[#b49678] text-sm">
                      {locale === 'ar' && selectedItem.ingredients_ar ? selectedItem.ingredients_ar : selectedItem.ingredients}
                    </p>
                  </div>
                )}
                
                {/* Allergens */}
                {(selectedItem.allergens || selectedItem.allergens_ar) && (
                  <div className="mb-6">
                    <h3 className="text-[#703f23] dark:text-[#e09a62] font-medium mb-2 flex items-center">
                      <i className="fa-solid fa-triangle-exclamation text-red-500 mr-2"></i>
                      {isClient ? t('menu.allergens') : 'Allergens'}
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {(() => {
                        const allergens = locale === 'ar' && selectedItem.allergens_ar ? selectedItem.allergens_ar : selectedItem.allergens;
                        return allergens?.split(',').map((allergen, index) => (
                          <span key={index} className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-xs px-3 py-1 rounded-full border border-red-200 dark:border-red-800">
                            {allergen.trim()}
                          </span>
                        ));
                      })()}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Add to Cart Section */}
              {selectedItem.stockStatus === StockStatus.IN_STOCK && (
                <div className="border-t border-[#dac6ae] dark:border-[#392e23] p-6">
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-[#703f23] dark:text-[#e09a62] font-medium">
                      {isClient ? t('menu.quantity') : 'Quantity'}
                    </span>
                    <ItemQuantitySelector 
                      initialQuantity={selectedQuantity}
                      onChange={setSelectedQuantity}
                      minQuantity={1}
                      maxQuantity={10}
                    />
                  </div>
                  
                  <button
                    onClick={handleAddToCart}
                    className="w-full bg-[#c27845] dark:bg-[#d27d46] text-white py-3 rounded-full font-medium hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-colors flex items-center justify-center"
                  >
                    <i className="fa-solid fa-cart-plus mr-2"></i>
                    {isClient ? t('menu.addToCart') : 'Add to Cart'}
                  </button>
                </div>
              )}
            </div>
          )}
        </SheetContent>
      </Sheet>
      
      {/* Cart Button */}
      <CartButton />
      
      {/* Footer with Social Icons */}
      <footer className="bg-[#1a1a1a]/80 backdrop-blur-sm py-8 mt-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-center gap-5 text-white mb-6">
            <a href="#" className="hover:opacity-80 transition-opacity text-lg hover:text-[#d27d46]">
              <i className="fa-solid fa-phone"></i>
            </a>
            <a href="#" className="hover:opacity-80 transition-opacity text-lg hover:text-[#d27d46]">
              <i className="fa-brands fa-whatsapp"></i>
            </a>
            <a href="#" className="hover:opacity-80 transition-opacity text-lg hover:text-[#d27d46]">
              <i className="fa-brands fa-instagram"></i>
            </a>
            <a href="#" className="hover:opacity-80 transition-opacity text-lg hover:text-[#d27d46]">
              <i className="fa-brands fa-snapchat"></i>
            </a>
            <a href="#" className="hover:opacity-80 transition-opacity text-lg hover:text-[#d27d46]">
              <i className="fa-brands fa-tiktok"></i>
            </a>
            <a href="#" className="hover:opacity-80 transition-opacity text-lg hover:text-[#d27d46]">
              <i className="fa-brands fa-x-twitter"></i>
            </a>
            <a href="#" className="hover:opacity-80 transition-opacity text-lg hover:text-[#d27d46]">
              <i className="fa-solid fa-location-dot"></i>
            </a>
          </div>
          <div className="text-center text-white/60 text-sm">
            <p>© {new Date().getFullYear()} Barcode Cafe. {isClient ? t('common.allRightsReserved') : 'All Rights Reserved.'}</p>
          </div>
        </div>
      </footer>

      {/* Package Detail Modal */}
      <PackageDetailModal
        isOpen={isPackageModalOpen}
        onClose={() => setIsPackageModalOpen(false)}
        offer={selectedPackage}
        packageItems={selectedPackageItems}
        allMenuItems={allMenuItems}
        onAddToCart={handleAddPackageToCart}
      />

      <style jsx global>{`
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        
        .custom-language-switcher button {
          background-color: white;
          color: #703f23;
        }
        
        .custom-language-switcher .lang-dropdown-menu {
          background-color: white;
          border-color: #c27845;
        }
        
        .dark .custom-language-switcher button {
          background-color: #2c3436;
          color: #e09a62;
        }
        
        .dark .custom-language-switcher .lang-dropdown-menu {
          background-color: #2c3436;
          border-color: #d27d46;
        }
        
        /* Style the built-in close button */
        [data-slot="sheet-content"] [data-radix-collection-item] {
          color: #703f23;
          background: transparent !important;
          opacity: 1;
        }
        
        .dark [data-slot="sheet-content"] [data-radix-collection-item] {
          color: #e09a62;
        }
      `}</style>
    </div>
  );
} 