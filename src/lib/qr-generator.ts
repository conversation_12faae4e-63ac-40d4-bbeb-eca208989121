import QRCode from 'qrcode';
import { QRStyle, QRStyleCategory } from '@/types/models';
import { createQRStyle, incrementQRStyleDownloadCount, incrementQRStylePrintCount } from '@/lib/firebase/firestore';

export interface QRCodeOptions {
  size: number;
  foregroundColor: string;
  backgroundColor: string;
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  margin?: number;
}

export interface QRCodeResult {
  dataURL: string;
  svg: string;
}

export interface SaveQRStyleOptions {
  userId: string;
  name: string;
  description?: string;
  tags?: string[];
  category?: QRStyleCategory;
}

/**
 * Generate QR code with custom styling options
 * @param text - The text/URL to encode in the QR code
 * @param options - Customization options for the QR code
 * @returns Promise with QR code data URL and SVG
 */
export async function generateQRCode(
  text: string,
  options: QRCodeOptions
): Promise<QRCodeResult> {
  const {
    size,
    foregroundColor,
    backgroundColor,
    errorCorrectionLevel = 'M',
    margin = 4
  } = options;

  try {
    // Generate PNG data URL
    const dataURL = await QRCode.toDataURL(text, {
      width: size,
      margin: margin,
      color: {
        dark: foregroundColor,
        light: backgroundColor
      },
      errorCorrectionLevel: errorCorrectionLevel
    });

    // Generate SVG string
    const svg = await QRCode.toString(text, {
      type: 'svg',
      width: size,
      margin: margin,
      color: {
        dark: foregroundColor,
        light: backgroundColor
      },
      errorCorrectionLevel: errorCorrectionLevel
    });

    return {
      dataURL,
      svg
    };
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

/**
 * Generate QR code for customer menu with default styling
 * @param baseURL - The base URL of the application
 * @param options - Optional customization options
 * @returns Promise with QR code result
 */
export async function generateMenuQRCode(
  baseURL: string,
  options: Partial<QRCodeOptions> = {}
): Promise<QRCodeResult> {
  const menuURL = `${baseURL}/menu`;
  
  const defaultOptions: QRCodeOptions = {
    size: 300,
    foregroundColor: '#56999B', // Default brand color
    backgroundColor: '#FFFFFF',
    errorCorrectionLevel: 'M',
    margin: 4,
    ...options
  };

  return generateQRCode(menuURL, defaultOptions);
}

/**
 * Save QR style to Firestore
 * @param qrResult - The generated QR code result
 * @param options - QR code options used to generate the QR
 * @param saveOptions - Options for saving the style
 * @param targetURL - The URL the QR code points to
 * @returns Promise with the saved QR style
 */
export async function saveQRStyle(
  qrResult: QRCodeResult,
  options: QRCodeOptions,
  saveOptions: SaveQRStyleOptions,
  targetURL: string
): Promise<QRStyle> {
  const qrStyleData: Omit<QRStyle, 'id' | 'createdAt' | 'updatedAt'> = {
    userId: saveOptions.userId,
    name: saveOptions.name,
    description: saveOptions.description,
    size: options.size,
    foregroundColor: options.foregroundColor,
    backgroundColor: options.backgroundColor,
    errorCorrectionLevel: options.errorCorrectionLevel || 'M',
    margin: options.margin || 4,
    qrDataURL: qrResult.dataURL,
    qrSVG: qrResult.svg,
    targetURL: targetURL,
    downloadCount: 0,
    printCount: 0,
    lastUsedAt: new Date(), // Set lastUsedAt when creating
    isTemplate: false,
    templateCategory: saveOptions.category || QRStyleCategory.CUSTOM,
    tags: saveOptions.tags || [],
  };

  return createQRStyle(qrStyleData);
}

/**
 * Download QR code as PNG file and track usage
 * @param dataURL - The QR code data URL
 * @param filename - The filename for the download
 * @param styleId - Optional QR style ID to track usage
 */
export async function downloadQRCodePNG(
  dataURL: string,
  filename: string,
  styleId?: string
): Promise<void> {
  const link = document.createElement('a');
  link.href = dataURL;
  link.download = filename.endsWith('.png') ? filename : `${filename}.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Track download if style ID is provided
  if (styleId) {
    try {
      await incrementQRStyleDownloadCount(styleId);
    } catch (error) {
      console.error('Error tracking download:', error);
    }
  }
}

/**
 * Download QR code as SVG file and track usage
 * @param svg - The QR code SVG string
 * @param filename - The filename for the download
 * @param styleId - Optional QR style ID to track usage
 */
export async function downloadQRCodeSVG(
  svg: string,
  filename: string,
  styleId?: string
): Promise<void> {
  const blob = new Blob([svg], { type: 'image/svg+xml' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename.endsWith('.svg') ? filename : `${filename}.svg`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  // Track download if style ID is provided
  if (styleId) {
    try {
      await incrementQRStyleDownloadCount(styleId);
    } catch (error) {
      console.error('Error tracking download:', error);
    }
  }
}

/**
 * Print QR code and track usage
 * @param dataURL - The QR code data URL
 * @param title - Optional title for the print
 * @param styleId - Optional QR style ID to track usage
 */
export async function printQRCode(
  dataURL: string,
  title?: string,
  styleId?: string
): Promise<void> {
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    throw new Error('Unable to open print window');
  }

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>${title || 'QR Code'}</title>
        <style>
          body {
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: Arial, sans-serif;
          }
          .qr-container {
            text-align: center;
            page-break-inside: avoid;
          }
          .qr-title {
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
          }
          .qr-image {
            max-width: 100%;
            height: auto;
          }
          @media print {
            body {
              margin: 0;
              padding: 10px;
            }
          }
        </style>
      </head>
      <body>
        <div class="qr-container">
          ${title ? `<div class="qr-title">${title}</div>` : ''}
          <img src="${dataURL}" alt="QR Code" class="qr-image" />
        </div>
      </body>
    </html>
  `;

  printWindow.document.write(html);
  printWindow.document.close();
  
  // Wait for image to load before printing
  printWindow.onload = () => {
    printWindow.print();
    printWindow.close();
  };

  // Track print if style ID is provided
  if (styleId) {
    try {
      await incrementQRStylePrintCount(styleId);
    } catch (error) {
      console.error('Error tracking print:', error);
    }
  }
}

/**
 * Generate filename for QR code based on colors and timestamp
 * @param foregroundColor - The foreground color
 * @param backgroundColor - The background color
 * @param prefix - Optional prefix for the filename
 * @returns Generated filename
 */
export function generateQRFilename(
  foregroundColor: string,
  backgroundColor: string,
  prefix: string = 'menu-qr'
): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
  const fgColor = foregroundColor.replace('#', '');
  const bgColor = backgroundColor.replace('#', '');
  
  return `${prefix}-${fgColor}-${bgColor}-${timestamp}`;
}

/**
 * Validate QR code options
 * @param options - The QR code options to validate
 * @returns Validation result
 */
export function validateQROptions(options: Partial<QRCodeOptions>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (options.size !== undefined) {
    if (options.size < 100 || options.size > 2000) {
      errors.push('Size must be between 100 and 2000 pixels');
    }
  }

  if (options.foregroundColor && !isValidHexColor(options.foregroundColor)) {
    errors.push('Invalid foreground color format');
  }

  if (options.backgroundColor && !isValidHexColor(options.backgroundColor)) {
    errors.push('Invalid background color format');
  }

  if (options.margin !== undefined) {
    if (options.margin < 0 || options.margin > 20) {
      errors.push('Margin must be between 0 and 20');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Check if a string is a valid hex color
 * @param color - The color string to validate
 * @returns True if valid hex color
 */
function isValidHexColor(color: string): boolean {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
}

/**
 * Get predefined color schemes for QR codes
 * @returns Array of predefined color schemes
 */
export function getQRColorSchemes(): Array<{
  name: string;
  name_ar?: string;
  foregroundColor: string;
  backgroundColor: string;
  description: string;
  description_ar?: string;
  category: QRStyleCategory;
}> {
  return [
    {
      name: 'Classic',
      name_ar: 'كلاسيكي',
      foregroundColor: '#000000',
      backgroundColor: '#FFFFFF',
      description: 'Traditional black on white',
      description_ar: 'أسود تقليدي على أبيض',
      category: QRStyleCategory.CLASSIC
    },
    {
      name: 'Brand',
      name_ar: 'العلامة التجارية',
      foregroundColor: '#56999B',
      backgroundColor: '#FFFFFF',
      description: 'BarcodeCafe brand colors',
      description_ar: 'ألوان علامة باركود كافيه التجارية',
      category: QRStyleCategory.BRAND
    },
    {
      name: 'Ocean',
      name_ar: 'المحيط',
      foregroundColor: '#0077BE',
      backgroundColor: '#E6F3FF',
      description: 'Ocean blue theme',
      description_ar: 'موضوع أزرق المحيط',
      category: QRStyleCategory.MODERN
    },
    {
      name: 'Forest',
      name_ar: 'الغابة',
      foregroundColor: '#228B22',
      backgroundColor: '#F0FFF0',
      description: 'Forest green theme',
      description_ar: 'موضوع أخضر الغابة',
      category: QRStyleCategory.MODERN
    },
    {
      name: 'Sunset',
      name_ar: 'غروب الشمس',
      foregroundColor: '#FF6347',
      backgroundColor: '#FFF8DC',
      description: 'Warm sunset colors',
      description_ar: 'ألوان غروب الشمس الدافئة',
      category: QRStyleCategory.SEASONAL
    },
    {
      name: 'Royal',
      name_ar: 'ملكي',
      foregroundColor: '#4B0082',
      backgroundColor: '#F8F8FF',
      description: 'Royal purple theme',
      description_ar: 'موضوع بنفسجي ملكي',
      category: QRStyleCategory.CLASSIC
    },
    {
      name: 'Midnight',
      name_ar: 'منتصف الليل',
      foregroundColor: '#FFFFFF',
      backgroundColor: '#1A1A1A',
      description: 'Dark theme with white QR',
      description_ar: 'موضوع داكن مع رمز QR أبيض',
      category: QRStyleCategory.MODERN
    },
    {
      name: 'Spring',
      name_ar: 'الربيع',
      foregroundColor: '#32CD32',
      backgroundColor: '#F0FFFF',
      description: 'Fresh spring colors',
      description_ar: 'ألوان الربيع المنعشة',
      category: QRStyleCategory.SEASONAL
    }
  ];
}

/**
 * Create predefined QR style templates in Firestore
 * @param userId - Admin user ID creating the templates
 * @returns Promise with created templates
 */
export async function createQRStyleTemplates(userId: string): Promise<QRStyle[]> {
  const schemes = getQRColorSchemes();
  const templates: QRStyle[] = [];

  for (const scheme of schemes) {
    try {
      // Generate QR code for the template
      const baseURL = typeof window !== 'undefined' ? window.location.origin : 'https://barcodecafe.com';
      const qrResult = await generateMenuQRCode(baseURL, {
        size: 300,
        foregroundColor: scheme.foregroundColor,
        backgroundColor: scheme.backgroundColor
      });

      const templateData: Omit<QRStyle, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: userId,
        name: scheme.name,
        description: scheme.description,
        size: 300,
        foregroundColor: scheme.foregroundColor,
        backgroundColor: scheme.backgroundColor,
        errorCorrectionLevel: 'M',
        margin: 4,
        qrDataURL: qrResult.dataURL,
        qrSVG: qrResult.svg,
        targetURL: `${baseURL}/menu`,
        downloadCount: 0,
        printCount: 0,
        isTemplate: true,
        templateCategory: scheme.category,
        tags: ['template', scheme.category],
      };

      const template = await createQRStyle(templateData);
      templates.push(template);
    } catch (error) {
      console.error(`Error creating template ${scheme.name}:`, error);
    }
  }

  return templates;
}
