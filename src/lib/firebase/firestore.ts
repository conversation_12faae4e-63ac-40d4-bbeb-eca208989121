import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  Timestamp, 
  serverTimestamp,
  DocumentReference,
  DocumentData,
  QueryConstraint,
  limit,
  setDoc,
  startAfter,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from './config';
import {
  UserProfile,
  Order,
  OrderItem,
  Address,
  GiftCard,
  Review,
  OrderStatus,
  PaymentMethod,
  Admin,
  Category,
  MenuItem,
  StockStatus,
  DeliveryZone,
  DeliveryZoneType,
  OrderCancellationData,
  OrderCancellationAuditEntry,
  OrderEditAuditEntry,
  Offer,
  OfferType,
  DiscountType,
  OfferConditions,
  AppliedDiscount,
  PackageItem,
  QRStyle,
  QRStyleCategory
} from '@/types/models';

// Helper to convert Firestore timestamp to Date
const convertTimestampToDate = (data: DocumentData | undefined): DocumentData => {
  if (!data) return {};

  const result = { ...data };

  // Convert Firestore timestamps to Date objects
  Object.keys(result).forEach(key => {
    const value = result[key];

    if (value instanceof Timestamp) {
      result[key] = value.toDate();
    } else if (value && typeof value === 'object' && '_methodName' in value && value._methodName === 'serverTimestamp') {
      // Handle unresolved serverTimestamp - use current date as fallback
      console.warn(`Unresolved serverTimestamp found for field ${key}, using current date as fallback`);
      result[key] = new Date();
    } else if (value && typeof value === 'object' && 'seconds' in value && 'nanoseconds' in value) {
      // Handle Firestore Timestamp-like objects
      try {
        result[key] = new Date(value.seconds * 1000 + value.nanoseconds / 1000000);
      } catch (error) {
        console.warn(`Failed to convert timestamp-like object for field ${key}:`, error);
        result[key] = new Date();
      }
    }
  });

  return result;
};

// Helper to prepare document for Firestore (handle Date objects and filter undefined values)
const prepareDocForFirestore = (data: Record<string, unknown>): DocumentData => {
  const result = { ...data };

  // Convert Date objects to Firestore timestamps and filter out undefined values
  Object.keys(result).forEach(key => {
    if (result[key] === undefined) {
      delete result[key];
    } else if (result[key] instanceof Date) {
      result[key] = Timestamp.fromDate(result[key] as Date);
    } else if (result[key] && typeof result[key] === 'object' && !Array.isArray(result[key])) {
      // Recursively process nested objects
      result[key] = prepareDocForFirestore(result[key] as Record<string, unknown>);
    } else if (Array.isArray(result[key])) {
      // Process arrays of objects
      result[key] = (result[key] as unknown[]).map(item =>
        item && typeof item === 'object' ? prepareDocForFirestore(item as Record<string, unknown>) : item
      );
    }
  });

  return result as DocumentData;
};

/**
 * Generic functions for collections
 */

// Get document by ID
export async function getDocumentById<T>(collectionName: string, id: string): Promise<T | null> {
  try {
    const docRef = doc(db, collectionName, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...convertTimestampToDate(docSnap.data()) } as T;
    }
    
    return null;
  } catch (error) {
    console.error(`Error getting document from ${collectionName}:`, error);
    throw error;
  }
}

// Get documents with filters
export async function getDocuments<T>(
  collectionName: string, 
  constraints: QueryConstraint[] = [],
  limitCount?: number
): Promise<T[]> {
  try {
    const queryConstraints = [...constraints];
    
    if (limitCount) {
      queryConstraints.push(limit(limitCount));
    }
    
    const q = query(collection(db, collectionName), ...queryConstraints);
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      return { id: doc.id, ...convertTimestampToDate(doc.data()) } as T;
    });
  } catch (error) {
    console.error(`Error getting documents from ${collectionName}:`, error);
    throw error;
  }
}

// Create document
export async function createDocument<T>(
  collectionName: string, 
  data: Omit<T, 'id'>,
  customId?: string
): Promise<T> {
  try {
    const preparedData = prepareDocForFirestore({
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    let docRef: DocumentReference<DocumentData>;
    
    if (customId) {
      docRef = doc(db, collectionName, customId);
      await setDoc(docRef, preparedData);
    } else {
      docRef = await addDoc(collection(db, collectionName), preparedData);
    }
    
    const newDocument = await getDoc(docRef);
    
    return { id: newDocument.id, ...convertTimestampToDate(newDocument.data()) } as T;
  } catch (error) {
    console.error(`Error creating document in ${collectionName}:`, error);
    throw error;
  }
}

// Update document
export async function updateDocument<T>(
  collectionName: string, 
  id: string, 
  data: Partial<T>
): Promise<T> {
  try {
    const docRef = doc(db, collectionName, id);
    const preparedData = prepareDocForFirestore({
      ...data,
      updatedAt: serverTimestamp()
    });
    
    await updateDoc(docRef, preparedData);
    
    const updatedDoc = await getDoc(docRef);
    
    return { id: updatedDoc.id, ...convertTimestampToDate(updatedDoc.data()) } as T;
  } catch (error) {
    console.error(`Error updating document in ${collectionName}:`, error);
    throw error;
  }
}

// Delete document
export async function deleteDocument(
  collectionName: string, 
  id: string
): Promise<void> {
  try {
    const docRef = doc(db, collectionName, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error(`Error deleting document from ${collectionName}:`, error);
    throw error;
  }
}

/**
 * User Profiles
 */
export const userProfilesCollection = 'userProfiles';

export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  return getDocumentById<UserProfile>(userProfilesCollection, userId);
}

export async function createUserProfile(userId: string, data: Partial<UserProfile>): Promise<UserProfile> {
  const defaultUserProfile: Omit<UserProfile, 'id'> = {
    uid: userId,
    displayName: data.displayName || '',
    email: data.email || '',
    photoURL: data.photoURL || '',
    phoneNumber: data.phoneNumber || '',
    loyaltyPoints: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    preferences: {
      language: 'en',
      darkMode: false,
      emailNotifications: true,
      pushNotifications: false,
      ...data.preferences
    }
  };
  
  return createDocument<UserProfile>(userProfilesCollection, defaultUserProfile, userId);
}

export async function updateUserProfile(userId: string, data: Partial<UserProfile>): Promise<UserProfile> {
  return updateDocument<UserProfile>(userProfilesCollection, userId, data);
}

/**
 * Admins
 */
export const adminsCollection = 'admins';

export async function getAdmin(userId: string): Promise<Admin | null> {
  console.log("Looking for admin document with ID:", userId);
  const result = await getDocumentById<Admin>(adminsCollection, userId);
  console.log("Admin document found:", result !== null, result);
  return result;
}

export async function createAdmin(userId: string, data: Partial<Admin>): Promise<Admin> {
  const defaultAdmin: Omit<Admin, 'id'> = {
    uid: userId,
    email: data.email || '',
    displayName: data.displayName || '',
    photoURL: data.photoURL || '',
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  return createDocument<Admin>(adminsCollection, defaultAdmin, userId);
}

export async function isUserAdmin(userId: string): Promise<boolean> {
  console.log("Checking if user is admin:", userId);
  const admin = await getAdmin(userId);
  const isAdmin = admin !== null;
  console.log("Is user admin result:", isAdmin);
  return isAdmin;
}

/**
 * Orders
 */
export const ordersCollection = 'orders';

export async function getOrder(orderId: string): Promise<Order | null> {
  return getDocumentById<Order>(ordersCollection, orderId);
}

export async function getUserOrders(userId: string, limitCount?: number): Promise<Order[]> {
  return getDocuments<Order>(
    ordersCollection, 
    [
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    ],
    limitCount
  );
}

export async function getUserOrdersByStatus(
  userId: string, 
  status: OrderStatus,
  limitCount?: number
): Promise<Order[]> {
  return getDocuments<Order>(
    ordersCollection,
    [
      where('userId', '==', userId),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    ],
    limitCount
  );
}

export async function createOrder(data: Omit<Order, 'id'>): Promise<Order> {
  return createDocument<Order>(ordersCollection, data);
}

export async function updateOrder(orderId: string, data: Partial<Order>): Promise<Order> {
  return updateDocument<Order>(ordersCollection, orderId, data);
}

// Order editing functions
export interface OrderEditData {
  items?: OrderItem[];
  deliveryType?: DeliveryZoneType;
  tableNumber?: string;
  deliveryAddress?: string;
  deliveryZoneId?: string;
  specialInstructions?: string;
  subtotal?: number;
  deliveryFee?: number;
  total?: number;
}



export async function canEditOrder(order: Order): Promise<{ canEdit: boolean; reason?: string }> {
  // Orders can only be edited if they are not completed, delivered, or cancelled
  const nonEditableStatuses = [OrderStatus.DELIVERED, OrderStatus.CANCELLED];

  if (nonEditableStatuses.includes(order.status)) {
    return {
      canEdit: false,
      reason: `Cannot edit orders with status: ${order.status}`
    };
  }

  return { canEdit: true };
}

export async function editOrder(
  orderId: string,
  editData: OrderEditData,
  adminId: string,
  adminEmail: string
): Promise<Order> {
  try {
    // Get the current order
    const currentOrder = await getOrder(orderId);
    if (!currentOrder) {
      throw new Error('Order not found');
    }

    // Check if order can be edited
    const { canEdit, reason } = await canEditOrder(currentOrder);
    if (!canEdit) {
      throw new Error(reason);
    }

    // Prepare audit trail entries
    const auditEntries: OrderEditAuditEntry[] = [];
    const timestamp = new Date();

    // Track changes and create audit entries
    if (editData.items && JSON.stringify(editData.items) !== JSON.stringify(currentOrder.items)) {
      auditEntries.push({
        timestamp,
        adminId,
        adminEmail,
        action: 'item_modified',
        details: 'Order items were modified',
        oldValue: currentOrder.items,
        newValue: editData.items
      });
    }

    if (editData.deliveryType && editData.deliveryType !== currentOrder.deliveryType) {
      auditEntries.push({
        timestamp,
        adminId,
        adminEmail,
        action: 'delivery_updated',
        details: `Delivery type changed from ${currentOrder.deliveryType} to ${editData.deliveryType}`,
        oldValue: currentOrder.deliveryType,
        newValue: editData.deliveryType
      });
    }

    if (editData.specialInstructions !== currentOrder.specialInstructions) {
      auditEntries.push({
        timestamp,
        adminId,
        adminEmail,
        action: 'instructions_updated',
        details: 'Special instructions were updated',
        oldValue: currentOrder.specialInstructions,
        newValue: editData.specialInstructions
      });
    }

    // Prepare update data
    const updateData: Partial<Order> = {
      ...editData,
      updatedAt: timestamp,
      // Add audit trail to the order (we'll store it as a field)
      ...(auditEntries.length > 0 && {
        editHistory: [...(currentOrder as any).editHistory || [], ...auditEntries]
      })
    };

    // Update the order
    const updatedOrder = await updateOrder(orderId, updateData);

    return updatedOrder;
  } catch (error) {
    console.error('Error editing order:', error);
    throw error;
  }
}

export function calculateOrderTotals(items: OrderItem[], deliveryFee: number = 0): {
  subtotal: number;
  total: number;
} {
  const subtotal = items.reduce((sum, item) => {
    const itemTotal = item.price * item.quantity;
    const optionsTotal = (item.options || []).reduce((optSum, option) =>
      optSum + (option.priceAdjustment * item.quantity), 0
    );
    return sum + itemTotal + optionsTotal;
  }, 0);

  const total = subtotal + deliveryFee;

  return { subtotal, total };
}

export async function deleteOrder(orderId: string): Promise<void> {
  return deleteDocument(ordersCollection, orderId);
}

// Order cancellation functions

export async function canCancelOrder(order: Order): Promise<{ canCancel: boolean; reason?: string }> {
  // Orders can only be cancelled if they are not already completed, delivered, or cancelled
  const nonCancellableStatuses = [OrderStatus.DELIVERED, OrderStatus.CANCELLED];

  if (nonCancellableStatuses.includes(order.status)) {
    return {
      canCancel: false,
      reason: `Cannot cancel orders with status: ${order.status}`
    };
  }

  // Check time limit for customer cancellations (e.g., within 5 minutes of order placement)
  const orderTime = order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt);
  const currentTime = new Date();
  const timeDifferenceMinutes = (currentTime.getTime() - orderTime.getTime()) / (1000 * 60);

  // For customer cancellations, allow cancellation within 5 minutes if order is still ORDER_PLACED
  if (order.status === OrderStatus.ORDER_PLACED && timeDifferenceMinutes > 5) {
    return {
      canCancel: false,
      reason: 'Order cancellation period has expired (5 minutes)'
    };
  }

  // For orders that are PREPARING or later, only admin can cancel
  if (order.status !== OrderStatus.ORDER_PLACED) {
    return {
      canCancel: true,
      reason: 'Only admin can cancel orders that are being prepared or later'
    };
  }

  return { canCancel: true };
}

export async function cancelOrder(
  orderId: string,
  cancellationData: OrderCancellationData
): Promise<Order> {
  try {
    // Get the current order
    const currentOrder = await getOrder(orderId);
    if (!currentOrder) {
      throw new Error('Order not found');
    }

    // Check if order can be cancelled
    const { canCancel, reason } = await canCancelOrder(currentOrder);
    if (!canCancel) {
      throw new Error(reason);
    }

    // Create audit entry
    const auditEntry: OrderCancellationAuditEntry = {
      timestamp: cancellationData.cancelledAt,
      userId: cancellationData.cancelledByUserId,
      userEmail: cancellationData.cancelledByEmail || '',
      userType: cancellationData.cancelledBy,
      action: 'order_cancelled',
      reason: cancellationData.reason,
      ...(cancellationData.refundAmount !== undefined && { refundAmount: cancellationData.refundAmount }),
      ...(cancellationData.refundMethod && { refundMethod: cancellationData.refundMethod }),
      ...(cancellationData.notes && { notes: cancellationData.notes })
    };

    // Prepare update data
    const updateData: Partial<Order> = {
      status: OrderStatus.CANCELLED,
      updatedAt: cancellationData.cancelledAt,
      cancellationData: cancellationData,
      cancellationHistory: [...(currentOrder.cancellationHistory || []), auditEntry]
    };

    // Update the order
    const updatedOrder = await updateOrder(orderId, updateData);

    return updatedOrder;
  } catch (error) {
    console.error('Error cancelling order:', error);
    throw error;
  }
}

// Helper functions for cancellation
export const CANCELLATION_REASONS = {
  CUSTOMER: [
    'changed_mind',
    'wrong_order',
    'delivery_delay',
    'payment_issue',
    'other'
  ],
  ADMIN: [
    'out_of_stock',
    'kitchen_issue',
    'delivery_unavailable',
    'customer_request',
    'payment_failed',
    'duplicate_order',
    'other'
  ]
} as const;

export function calculateRefundAmount(order: Order, cancellationTime: Date): number {
  // For cash orders, full refund is typically given within the allowed time window
  if (order.paymentMethod === PaymentMethod.CASH) {
    const orderTime = order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt);
    const timeDifferenceMinutes = (cancellationTime.getTime() - orderTime.getTime()) / (1000 * 60);

    // Full refund if cancelled within 5 minutes
    if (timeDifferenceMinutes <= 5 && order.status === OrderStatus.ORDER_PLACED) {
      return order.total;
    }

    // No refund for cash orders after 5 minutes (admin discretion)
    return 0;
  }

  // For other payment methods, calculate based on timing and status
  const orderTime = order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt);
  const timeDifferenceMinutes = (cancellationTime.getTime() - orderTime.getTime()) / (1000 * 60);

  // Full refund if cancelled within 5 minutes
  if (timeDifferenceMinutes <= 5 && order.status === OrderStatus.ORDER_PLACED) {
    return order.total;
  }

  // No refund for late cancellations (admin discretion)
  return 0;
}

export function getRefundMethod(order: Order): string {
  switch (order.paymentMethod) {
    case PaymentMethod.CASH:
      return 'cash_refund';
    case PaymentMethod.CREDIT_CARD:
    case PaymentMethod.DEBIT_CARD:
      return 'card_refund';
    case PaymentMethod.GIFT_CARD:
      return 'gift_card_credit';
    case PaymentMethod.LOYALTY_POINTS:
      return 'points_credit';
    default:
      return 'manual_refund';
  }
}

// Admin order management functions
export async function getAllOrders(limitCount?: number): Promise<Order[]> {
  return getDocuments<Order>(
    ordersCollection,
    [orderBy('createdAt', 'desc')],
    limitCount
  );
}

export async function getOrdersByStatus(status: OrderStatus, limitCount?: number): Promise<Order[]> {
  return getDocuments<Order>(
    ordersCollection,
    [
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    ],
    limitCount
  );
}

// Dashboard statistics functions
export async function getTotalOrdersCount(): Promise<number> {
  try {
    const q = query(collection(db, ordersCollection));
    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting total orders count:', error);
    throw error;
  }
}

export async function getTodaysSales(): Promise<number> {
  try {
    // Get all orders
    const allOrders = await getAllOrders(100);
    console.log(`🔍 DEBUG: Total orders in database: ${allOrders.length}`);

    if (allOrders.length === 0) {
      console.log('⚠️ No orders found in database');
      return 0;
    }

    // Get today's date info
    const today = new Date();
    const todayString = today.toDateString();
    console.log(`📅 Today is: ${todayString} (${today.toISOString()})`);

    // Filter orders for today and calculate sales
    let totalSales = 0;
    let validOrdersCount = 0;
    let todayOrdersCount = 0;

    console.log('📋 Processing orders:');
    allOrders.forEach((order, index) => {
      try {
        // The convertTimestampToDate function should have already converted dates
        let orderDate: Date;

        if (order.createdAt instanceof Date) {
          orderDate = order.createdAt;
        } else {
          // Fallback: try to parse as string or handle timestamp object
          orderDate = new Date(order.createdAt);
        }

        // Validate the date
        if (isNaN(orderDate.getTime())) {
          console.log(`⚠️ Order ${order.id.slice(-8)} has invalid date, skipping`);
          return;
        }

        const orderDateString = orderDate.toDateString();
        console.log(`${index + 1}. Order ${order.id.slice(-8)}: SAR ${order.total}, Status: ${order.status}, Date: ${orderDateString}`);

        // Check if order is from today
        if (orderDateString === todayString) {
          todayOrdersCount++;
          console.log(`📦 Today's order found: ${order.id.slice(-8)}, Status: ${order.status}, Total: SAR ${order.total}`);

          // Check if not cancelled
          if (order.status !== OrderStatus.CANCELLED) {
            totalSales += order.total || 0;
            validOrdersCount++;
            console.log(`✅ Added to sales: SAR ${order.total}`);
          } else {
            console.log(`❌ Cancelled order excluded: ${order.id.slice(-8)}`);
          }
        }
      } catch (error) {
        console.log(`❌ Error processing order ${order.id.slice(-8)}:`, error);
      }
    });

    console.log(`📊 Summary:`);
    console.log(`- Orders from today: ${todayOrdersCount}`);
    console.log(`- Valid (non-cancelled) orders: ${validOrdersCount}`);
    console.log(`- Total sales: SAR ${totalSales}`);

    return totalSales;
  } catch (error) {
    console.error('❌ Error getting today\'s sales:', error);
    throw error;
  }
}

export async function getRecentOrdersForAdmin(limitCount: number = 5): Promise<Order[]> {
  return getDocuments<Order>(
    ordersCollection,
    [orderBy('createdAt', 'desc')],
    limitCount
  );
}

// Debug function to create a test order for today (for testing purposes)
export async function createTestOrderForToday(): Promise<Order> {
  const testOrder: Omit<Order, 'id'> = {
    userId: 'test-user',
    customerName: 'Test Customer',
    customerEmail: '<EMAIL>',
    items: [
      {
        id: 'test-item-1',
        name: 'Test Coffee',
        price: 25.50,
        quantity: 2,
        options: []
      }
    ],
    subtotal: 51.00,
    deliveryFee: 5.00,
    total: 56.00,
    status: OrderStatus.DELIVERED,
    paymentMethod: PaymentMethod.CASH,
    deliveryType: 'pickup' as DeliveryZoneType,
    specialInstructions: 'Test order for sales calculation',
    createdAt: new Date(), // Today's date
    updatedAt: new Date()
  };

  console.log('🧪 Creating test order for today with total: SAR', testOrder.total);
  return createOrder(testOrder);
}

export async function searchOrders(searchTerm: string, limitCount?: number): Promise<Order[]> {
  // Note: This is a basic implementation. For production, consider using Algolia or similar
  // for more advanced search capabilities
  const allOrders = await getAllOrders(limitCount);

  return allOrders.filter(order => {
    const searchLower = searchTerm.toLowerCase();
    return (
      order.id.toLowerCase().includes(searchLower) ||
      order.items.some(item => item.name.toLowerCase().includes(searchLower)) ||
      (order.deliveryAddress && order.deliveryAddress.toLowerCase().includes(searchLower)) ||
      (order.tableNumber && order.tableNumber.toLowerCase().includes(searchLower))
    );
  });
}

export async function getOrdersWithPagination(
  lastVisible?: DocumentSnapshot,
  pageSize: number = 20,
  status?: OrderStatus
): Promise<{ orders: Order[], lastVisible: DocumentSnapshot | null }> {
  const constraints: QueryConstraint[] = [];

  if (status) {
    constraints.push(where('status', '==', status));
  }

  constraints.push(orderBy('createdAt', 'desc'));

  let q = query(collection(db, ordersCollection), ...constraints);

  if (lastVisible) {
    q = query(q, startAfter(lastVisible), limit(pageSize));
  } else {
    q = query(q, limit(pageSize));
  }

  const querySnapshot = await getDocs(q);
  const lastVisibleDoc = querySnapshot.docs[querySnapshot.docs.length - 1] || null;

  const orders = querySnapshot.docs.map(doc => {
    return { id: doc.id, ...convertTimestampToDate(doc.data()) } as Order;
  });

  return { orders, lastVisible: lastVisibleDoc };
}

/**
 * Addresses
 */
export const addressesCollection = 'addresses';

export async function getAddress(addressId: string): Promise<Address | null> {
  return getDocumentById<Address>(addressesCollection, addressId);
}

export async function getUserAddresses(userId: string): Promise<Address[]> {
  return getDocuments<Address>(
    addressesCollection,
    [
      where('userId', '==', userId),
      orderBy('isDefault', 'desc'),
      orderBy('createdAt', 'desc')
    ]
  );
}

export async function getDefaultAddress(userId: string): Promise<Address | null> {
  const addresses = await getDocuments<Address>(
    addressesCollection,
    [
      where('userId', '==', userId),
      where('isDefault', '==', true),
      limit(1)
    ]
  );
  
  return addresses.length > 0 ? addresses[0] : null;
}

export async function createAddress(data: Omit<Address, 'id'>): Promise<Address> {
  // If setting as default, update all other addresses to not be default
  if (data.isDefault) {
    const userAddresses = await getUserAddresses(data.userId);
    
    await Promise.all(
      userAddresses
        .filter(address => address.isDefault)
        .map(address => 
          updateDocument<Address>(addressesCollection, address.id, { isDefault: false })
        )
    );
  }
  
  return createDocument<Address>(addressesCollection, data);
}

export async function updateAddress(addressId: string, data: Partial<Address>): Promise<Address> {
  // If updating to be default, update all other addresses to not be default
  if (data.isDefault) {
    const address = await getAddress(addressId);
    
    if (address) {
      const userAddresses = await getUserAddresses(address.userId);
      
      await Promise.all(
        userAddresses
          .filter(a => a.id !== addressId && a.isDefault)
          .map(a => 
            updateDocument<Address>(addressesCollection, a.id, { isDefault: false })
          )
      );
    }
  }
  
  return updateDocument<Address>(addressesCollection, addressId, data);
}

export async function deleteAddress(addressId: string): Promise<void> {
  return deleteDocument(addressesCollection, addressId);
}

/**
 * Gift Cards
 */
export const giftCardsCollection = 'giftCards';

export async function getGiftCard(giftCardId: string): Promise<GiftCard | null> {
  return getDocumentById<GiftCard>(giftCardsCollection, giftCardId);
}

export async function getGiftCardByCode(code: string): Promise<GiftCard | null> {
  const giftCards = await getDocuments<GiftCard>(
    giftCardsCollection,
    [
      where('code', '==', code),
      limit(1)
    ]
  );
  
  return giftCards.length > 0 ? giftCards[0] : null;
}

export async function getUserGiftCards(userId: string): Promise<GiftCard[]> {
  return getDocuments<GiftCard>(
    giftCardsCollection,
    [
      where('userId', '==', userId),
      orderBy('expiryDate', 'desc')
    ]
  );
}

export async function createGiftCard(data: Omit<GiftCard, 'id'>): Promise<GiftCard> {
  return createDocument<GiftCard>(giftCardsCollection, data);
}

export async function updateGiftCard(giftCardId: string, data: Partial<GiftCard>): Promise<GiftCard> {
  return updateDocument<GiftCard>(giftCardsCollection, giftCardId, data);
}

export async function deleteGiftCard(giftCardId: string): Promise<void> {
  return deleteDocument(giftCardsCollection, giftCardId);
}

export async function getUserGiftCardBalance(userId: string): Promise<number> {
  try {
    const userGiftCards = await getUserGiftCards(userId);

    // Calculate total balance from all active, non-expired gift cards
    const totalBalance = userGiftCards.reduce((total, giftCard) => {
      // Check if gift card is active and not expired
      if (giftCard.isActive && giftCard.currentBalance > 0) {
        const expiryDate = giftCard.expiryDate instanceof Date
          ? giftCard.expiryDate
          : new Date(giftCard.expiryDate);

        // Only include if not expired
        if (expiryDate > new Date()) {
          return total + giftCard.currentBalance;
        }
      }
      return total;
    }, 0);

    return totalBalance;
  } catch (error) {
    console.error('Error getting user gift card balance:', error);
    throw error;
  }
}

/**
 * Reviews
 */
export const reviewsCollection = 'reviews';

export async function getReview(reviewId: string): Promise<Review | null> {
  return getDocumentById<Review>(reviewsCollection, reviewId);
}

export async function getUserReviews(userId: string): Promise<Review[]> {
  return getDocuments<Review>(
    reviewsCollection,
    [
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    ]
  );
}

// Get all reviews for admin interface
export async function getAllReviews(): Promise<Review[]> {
  return getDocuments<Review>(
    reviewsCollection,
    [
      orderBy('createdAt', 'desc')
    ]
  );
}

// Get reviews by order ID
export async function getOrderReviews(orderId: string): Promise<Review[]> {
  return getDocuments<Review>(
    reviewsCollection,
    [
      where('orderId', '==', orderId),
      orderBy('createdAt', 'desc')
    ]
  );
}

export async function createReview(data: Omit<Review, 'id'>): Promise<Review> {
  return createDocument<Review>(reviewsCollection, data);
}

export async function updateReview(reviewId: string, data: Partial<Review>): Promise<Review> {
  return updateDocument<Review>(reviewsCollection, reviewId, data);
}

export async function deleteReview(reviewId: string): Promise<void> {
  return deleteDocument(reviewsCollection, reviewId);
}

/**
 * Categories
 */
export const categoriesCollection = 'categories';

export async function getCategory(categoryId: string): Promise<Category | null> {
  return getDocumentById<Category>(categoriesCollection, categoryId);
}

export async function getUserCategories(userId: string): Promise<Category[]> {
  return getDocuments<Category>(
    categoriesCollection,
    [
      where('userId', '==', userId),
      orderBy('displayOrder', 'asc')
    ]
  );
}

export async function getUserFeaturedCategories(userId: string): Promise<Category[]> {
  return getDocuments<Category>(
    categoriesCollection,
    [
      where('userId', '==', userId),
      where('isFeatured', '==', true),
      orderBy('displayOrder', 'asc')
    ]
  );
}

export async function getUserVisibleCategories(userId: string): Promise<Category[]> {
  return getDocuments<Category>(
    categoriesCollection,
    [
      where('userId', '==', userId),
      where('isVisible', '==', true),
      orderBy('displayOrder', 'asc')
    ]
  );
}

export async function createCategory(data: Omit<Category, 'id'>): Promise<Category> {
  return createDocument<Category>(categoriesCollection, data);
}

export async function updateCategory(categoryId: string, data: Partial<Category>): Promise<Category> {
  return updateDocument<Category>(categoriesCollection, categoryId, data);
}

export async function deleteCategory(categoryId: string): Promise<void> {
  return deleteDocument(categoriesCollection, categoryId);
}

export async function getCategoryCount(userId: string): Promise<number> {
  const categories = await getUserCategories(userId);
  return categories.length;
}

export async function getActiveCategoryCount(userId: string): Promise<number> {
  const categories = await getDocuments<Category>(
    categoriesCollection,
    [
      where('userId', '==', userId),
      where('isActive', '==', true)
    ]
  );
  return categories.length;
}

export async function getFeaturedCategoryCount(userId: string): Promise<number> {
  const categories = await getUserFeaturedCategories(userId);
  return categories.length;
}

/**
 * Menu Items
 */
export const menuItemsCollection = 'menuItems';

export async function getMenuItem(menuItemId: string): Promise<MenuItem | null> {
  return getDocumentById<MenuItem>(menuItemsCollection, menuItemId);
}

export async function getUserMenuItems(
  userId: string,
  pageSize: number = 10,
  lastVisible: DocumentSnapshot | null = null,
  categoryId?: string,
  stockStatus?: StockStatus,
  searchTerm?: string,
  onlyActive?: boolean
): Promise<{ items: MenuItem[], lastVisible: DocumentSnapshot | null }> {
  try {
    const constraints: QueryConstraint[] = [
      where('userId', '==', userId),
    ];
    
    if (categoryId) {
      constraints.push(where('categoryId', '==', categoryId));
    }
    
    if (stockStatus) {
      constraints.push(where('stockStatus', '==', stockStatus));
    }
    
    if (onlyActive) {
      constraints.push(where('isActive', '==', true));
    }
    
    // Add ordering
    constraints.push(orderBy('createdAt', 'desc'));
    
    // Handle pagination
    let q = query(collection(db, menuItemsCollection), ...constraints);
    
    if (lastVisible) {
      q = query(q, startAfter(lastVisible), limit(pageSize));
    } else {
      q = query(q, limit(pageSize));
    }
    
    const querySnapshot = await getDocs(q);
    
    // Get the last document for pagination
    const lastVisibleDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
    
    let items = querySnapshot.docs.map(doc => {
      return { id: doc.id, ...convertTimestampToDate(doc.data()) } as MenuItem;
    });
    
    // If search term is provided, filter the results client-side (not ideal but works for small datasets)
    if (searchTerm && searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase();
      items = items.filter(item => 
        item.title.toLowerCase().includes(searchLower) || 
        item.description.toLowerCase().includes(searchLower)
      );
    }
    
    return { items, lastVisible: lastVisibleDoc };
  } catch (error) {
    console.error('Error getting menu items:', error);
    throw error;
  }
}

export async function getUserMenuItemCount(userId: string): Promise<number> {
  try {
    const constraints: QueryConstraint[] = [
      where('userId', '==', userId)
    ];
    
    const q = query(collection(db, menuItemsCollection), ...constraints);
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting menu item count:', error);
    throw error;
  }
}

export async function getMenuItemsByCategory(
  userId: string,
  categoryId: string
): Promise<MenuItem[]> {
  return getDocuments<MenuItem>(
    menuItemsCollection,
    [
      where('userId', '==', userId),
      where('categoryId', '==', categoryId),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    ]
  );
}

export async function createMenuItem(data: Omit<MenuItem, 'id'>): Promise<MenuItem> {
  return createDocument<MenuItem>(menuItemsCollection, data);
}

export async function updateMenuItem(menuItemId: string, data: Partial<MenuItem>): Promise<MenuItem> {
  return updateDocument<MenuItem>(menuItemsCollection, menuItemId, data);
}

export async function deleteMenuItem(menuItemId: string): Promise<void> {
  return deleteDocument(menuItemsCollection, menuItemId);
}

export async function updateCategoryItemCount(categoryId: string, userId: string): Promise<void> {
  if (!categoryId) return;

  try {
    const items = await getMenuItemsByCategory(userId, categoryId);
    await updateCategory(categoryId, { itemCount: items.length });
  } catch (error) {
    console.error('Error updating category item count:', error);
    throw error;
  }
}

// Get all active menu items for admin order editing
export async function getAllActiveMenuItems(userId: string): Promise<MenuItem[]> {
  return getDocuments<MenuItem>(
    menuItemsCollection,
    [
      where('userId', '==', userId),
      where('isActive', '==', true),
      orderBy('title', 'asc')
    ]
  );
}

/**
 * Delivery Zones
 */
export const deliveryZonesCollection = 'deliveryZones';

export async function getDeliveryZone(zoneId: string): Promise<DeliveryZone | null> {
  return getDocumentById<DeliveryZone>(deliveryZonesCollection, zoneId);
}

export async function getUserDeliveryZones(userId: string): Promise<DeliveryZone[]> {
  return getDocuments<DeliveryZone>(
    deliveryZonesCollection,
    [
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    ]
  );
}

export async function getUserDeliveryZonesByType(userId: string, type: DeliveryZoneType): Promise<DeliveryZone[]> {
  return getDocuments<DeliveryZone>(
    deliveryZonesCollection,
    [
      where('userId', '==', userId),
      where('type', '==', type),
      orderBy('createdAt', 'desc')
    ]
  );
}

/**
 * Get all delivery zones by type without filtering by user ID
 * Used for customer-facing UI to display available delivery options
 */
export async function getDeliveryZonesByType(type: DeliveryZoneType): Promise<DeliveryZone[]> {
  return getDocuments<DeliveryZone>(
    deliveryZonesCollection,
    [
      where('type', '==', type),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    ]
  );
}

export async function createDeliveryZone(data: Omit<DeliveryZone, 'id'>): Promise<DeliveryZone> {
  return createDocument<DeliveryZone>(deliveryZonesCollection, data);
}

export async function updateDeliveryZone(zoneId: string, data: Partial<DeliveryZone>): Promise<DeliveryZone> {
  return updateDocument<DeliveryZone>(deliveryZonesCollection, zoneId, data);
}

export async function deleteDeliveryZone(zoneId: string): Promise<void> {
  return deleteDocument(deliveryZonesCollection, zoneId);
}

/**
 * Offers & Discounts
 */
export const offersCollection = 'offers';

export async function getOffer(offerId: string): Promise<Offer | null> {
  return getDocumentById<Offer>(offersCollection, offerId);
}

export async function createOffer(offerData: Omit<Offer, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Offer> {
  const defaultOffer: Omit<Offer, 'id'> = {
    ...offerData,
    usageCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return createDocument<Offer>(offersCollection, defaultOffer);
}

export async function updateOffer(offerId: string, updates: Partial<Offer>): Promise<Offer> {
  const updateData = {
    ...updates,
    updatedAt: new Date(),
  };

  return updateDocument<Offer>(offersCollection, offerId, updateData);
}

export async function deleteOffer(offerId: string): Promise<void> {
  return deleteDocument(offersCollection, offerId);
}

export async function getOffers(userId?: string): Promise<Offer[]> {
  const constraints: QueryConstraint[] = [];

  if (userId) {
    constraints.push(where('userId', '==', userId));
  }

  constraints.push(orderBy('createdAt', 'desc'));

  return getDocuments<Offer>(offersCollection, constraints);
}

export async function getActiveOffers(userId?: string): Promise<Offer[]> {
  const constraints: QueryConstraint[] = [
    where('isActive', '==', true),
    where('startDate', '<=', new Date()),
    where('endDate', '>=', new Date())
  ];

  if (userId) {
    constraints.push(where('userId', '==', userId));
  }

  constraints.push(orderBy('createdAt', 'desc'));

  return getDocuments<Offer>(offersCollection, constraints);
}

// Offer validation and calculation functions
export function validateOfferConditions(
  offer: Offer,
  cartItems: { id: string; categoryId: string; price: number; quantity: number; isAvailableForDelivery: boolean }[],
  deliveryType?: DeliveryZoneType,
  userLoyaltyPoints?: number
): { isValid: boolean; reason?: string } {
  const now = new Date();
  const conditions = offer.conditions;

  // Check if offer is active and within date range
  if (!offer.isActive) {
    return { isValid: false, reason: 'Offer is not active' };
  }

  if (new Date(offer.startDate) > now) {
    return { isValid: false, reason: 'Offer has not started yet' };
  }

  if (new Date(offer.endDate) < now) {
    return { isValid: false, reason: 'Offer has expired' };
  }

  // Check usage limits
  if (offer.usageLimit && offer.usageCount >= offer.usageLimit) {
    return { isValid: false, reason: 'Offer usage limit reached' };
  }

  // Calculate cart total
  const cartTotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  // Check minimum order amount
  if (conditions.minOrderAmount && cartTotal < conditions.minOrderAmount) {
    return { isValid: false, reason: `Minimum order amount of SAR ${conditions.minOrderAmount} required` };
  }

  // Check delivery type restrictions
  if (conditions.deliveryTypesOnly && conditions.deliveryTypesOnly.length > 0) {
    if (!deliveryType || !conditions.deliveryTypesOnly.includes(deliveryType)) {
      return { isValid: false, reason: 'Offer not valid for selected delivery type' };
    }
  }

  // Check loyalty points requirement
  if (conditions.loyaltyPointsRequired && (!userLoyaltyPoints || userLoyaltyPoints < conditions.loyaltyPointsRequired)) {
    return { isValid: false, reason: `Requires ${conditions.loyaltyPointsRequired} loyalty points` };
  }

  // Check applicable categories
  if (conditions.applicableCategories && conditions.applicableCategories.length > 0) {
    const hasApplicableItems = cartItems.some(item =>
      conditions.applicableCategories!.includes(item.categoryId)
    );
    if (!hasApplicableItems) {
      return { isValid: false, reason: 'No applicable items in cart' };
    }
  }

  // Check applicable menu items
  if (conditions.applicableMenuItems && conditions.applicableMenuItems.length > 0) {
    const hasApplicableItems = cartItems.some(item =>
      conditions.applicableMenuItems!.includes(item.id)
    );
    if (!hasApplicableItems) {
      return { isValid: false, reason: 'No applicable items in cart' };
    }
  }

  // Check day of week restriction
  if (conditions.dayOfWeek && conditions.dayOfWeek.length > 0) {
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    if (!conditions.dayOfWeek.includes(currentDay)) {
      return { isValid: false, reason: 'Offer not valid on this day' };
    }
  }

  // Check time of day restriction
  if (conditions.timeOfDay) {
    const currentTime = now.toTimeString().slice(0, 5); // "HH:MM"
    if (currentTime < conditions.timeOfDay.start || currentTime > conditions.timeOfDay.end) {
      return { isValid: false, reason: 'Offer not valid at this time' };
    }
  }

  // Special validation for package deals
  if (offer.type === OfferType.PACKAGE_DEAL) {
    // Handle both data structures: packageItems (new) and applicableMenuItems (legacy)
    if (conditions.packageItems && conditions.packageItems.length > 0) {
      // New structure: Check if cart contains all required package items in sufficient quantities
      for (const packageItem of conditions.packageItems) {
        const cartItem = cartItems.find(item => item.id === packageItem.menuItemId);
        if (!cartItem) {
          return { isValid: false, reason: 'Cart does not contain all required package items' };
        }
        if (cartItem.quantity < packageItem.quantity) {
          return { isValid: false, reason: 'Insufficient quantity of package items in cart' };
        }
      }
    } else if (conditions.applicableMenuItems && conditions.applicableMenuItems.length > 0) {
      // Legacy structure: Check if cart contains at least one of each applicable menu item
      for (const menuItemId of conditions.applicableMenuItems) {
        const cartItem = cartItems.find(item => item.id === menuItemId);
        if (!cartItem) {
          return { isValid: false, reason: 'Cart does not contain all required package items' };
        }
        // For legacy structure, assume minimum quantity of 1 per item
        if (cartItem.quantity < 1) {
          return { isValid: false, reason: 'Insufficient quantity of package items in cart' };
        }
      }
    } else {
      return { isValid: false, reason: 'Package deal configuration is incomplete' };
    }
  }

  return { isValid: true };
}

export function calculateDiscountAmount(
  offer: Offer,
  cartItems: { id: string; categoryId: string; price: number; quantity: number }[],
  deliveryFee: number = 0
): number {
  const conditions = offer.conditions;
  let discountAmount = 0;

  // Calculate base amount to apply discount to
  let applicableAmount = 0;

  switch (offer.discountType) {
    case DiscountType.ORDER_TOTAL:
      applicableAmount = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      break;

    case DiscountType.CATEGORY:
      if (conditions.applicableCategories) {
        applicableAmount = cartItems
          .filter(item => conditions.applicableCategories!.includes(item.categoryId))
          .reduce((sum, item) => sum + (item.price * item.quantity), 0);
      }
      break;

    case DiscountType.MENU_ITEM:
      if (conditions.applicableMenuItems) {
        applicableAmount = cartItems
          .filter(item => conditions.applicableMenuItems!.includes(item.id))
          .reduce((sum, item) => sum + (item.price * item.quantity), 0);
      }
      break;

    case DiscountType.DELIVERY_FEE:
      applicableAmount = deliveryFee;
      break;
  }

  // Calculate discount based on offer type
  switch (offer.type) {
    case OfferType.PERCENTAGE:
      discountAmount = (applicableAmount * offer.discountValue) / 100;
      break;

    case OfferType.FIXED_AMOUNT:
      discountAmount = Math.min(offer.discountValue, applicableAmount);
      break;

    case OfferType.FREE_DELIVERY:
      if (offer.discountType === DiscountType.DELIVERY_FEE) {
        discountAmount = deliveryFee;
      }
      break;

    case OfferType.PACKAGE_DEAL:
      if (conditions.packagePrice) {
        let packageItemsTotal = 0;

        // Handle both data structures: packageItems (new) and applicableMenuItems (legacy)
        if (conditions.packageItems && conditions.packageItems.length > 0) {
          // New structure: Calculate total based on specific quantities
          packageItemsTotal = conditions.packageItems.reduce((sum, packageItem) => {
            const cartItem = cartItems.find(item => item.id === packageItem.menuItemId);
            return sum + (cartItem ? cartItem.price * packageItem.quantity : 0);
          }, 0);
        } else if (conditions.applicableMenuItems && conditions.applicableMenuItems.length > 0) {
          // Legacy structure: Calculate total based on cart quantities (minimum 1 each)
          packageItemsTotal = conditions.applicableMenuItems.reduce((sum, menuItemId) => {
            const cartItem = cartItems.find(item => item.id === menuItemId);
            return sum + (cartItem ? cartItem.price * Math.min(cartItem.quantity, 1) : 0);
          }, 0);
        }

        discountAmount = Math.max(0, packageItemsTotal - conditions.packagePrice);
      }
      break;

    case OfferType.BUY_X_GET_Y:
      // This would require more complex logic based on specific buy X get Y rules
      // For now, we'll treat it as a percentage discount
      discountAmount = (applicableAmount * offer.discountValue) / 100;
      break;

    case OfferType.LOYALTY_POINTS:
      discountAmount = Math.min(offer.discountValue, applicableAmount);
      break;
  }

  // Apply maximum discount cap if specified
  if (conditions.maxDiscountAmount) {
    discountAmount = Math.min(discountAmount, conditions.maxDiscountAmount);
  }

  return Math.max(0, Math.round(discountAmount * 100) / 100); // Round to 2 decimal places
}

export async function incrementOfferUsage(offerId: string): Promise<void> {
  const offer = await getOffer(offerId);
  if (offer) {
    await updateOffer(offerId, {
      usageCount: offer.usageCount + 1
    });
  }
}

// Get package deals (offers with type PACKAGE_DEAL)
export async function getActivePackageDeals(): Promise<Offer[]> {
  const constraints: QueryConstraint[] = [
    where('isActive', '==', true),
    where('type', '==', OfferType.PACKAGE_DEAL),
    where('startDate', '<=', new Date()),
    where('endDate', '>=', new Date()),
    orderBy('createdAt', 'desc')
  ];

  return getDocuments<Offer>(offersCollection, constraints);
}

// Get menu items for a package deal
export async function getPackageMenuItems(packageItems: PackageItem[]): Promise<MenuItem[]> {
  if (!packageItems || packageItems.length === 0) {
    return [];
  }

  const menuItemIds = packageItems.map(item => item.menuItemId);
  const menuItems: MenuItem[] = [];

  // Fetch menu items individually using document IDs
  for (const itemId of menuItemIds) {
    try {
      const docRef = doc(db, 'menuItems', itemId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data() as Omit<MenuItem, 'id'>;
        // Only include active menu items
        if (data.isActive) {
          menuItems.push({
            id: docSnap.id,
            ...data,
            createdAt: data.createdAt instanceof Date ? data.createdAt : new Date(data.createdAt as string),
            updatedAt: data.updatedAt instanceof Date ? data.updatedAt : new Date(data.updatedAt as string),
          });
        }
      }
    } catch (error) {
      console.error(`Error fetching menu item ${itemId}:`, error);
    }
  }

  return menuItems;
}

/**
 * QR Styles
 */
export const qrStylesCollection = 'qrStyles';

export async function getQRStyle(styleId: string): Promise<QRStyle | null> {
  return getDocumentById<QRStyle>(qrStylesCollection, styleId);
}

export async function getUserQRStyles(
  userId: string,
  pageSize: number = 20,
  lastVisible: DocumentSnapshot | null = null,
  searchTerm?: string,
  category?: QRStyleCategory
): Promise<{ styles: QRStyle[], lastVisible: DocumentSnapshot | null }> {
  try {
    const constraints: QueryConstraint[] = [
      where('userId', '==', userId),
      where('isTemplate', '==', false) // Only user-created styles, not templates
    ];

    if (category) {
      constraints.push(where('templateCategory', '==', category));
    }

    // Add ordering - use createdAt as primary sort since lastUsedAt might be null
    constraints.push(orderBy('createdAt', 'desc'));

    // Handle pagination
    let q = query(collection(db, qrStylesCollection), ...constraints);

    if (lastVisible) {
      q = query(q, startAfter(lastVisible), limit(pageSize));
    } else {
      q = query(q, limit(pageSize));
    }

    const querySnapshot = await getDocs(q);

    // Get the last document for pagination
    const lastVisibleDoc = querySnapshot.docs[querySnapshot.docs.length - 1];

    let styles = querySnapshot.docs.map(doc => {
      return { id: doc.id, ...convertTimestampToDate(doc.data()) } as QRStyle;
    });

    // If search term is provided, filter the results client-side
    if (searchTerm && searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase();
      styles = styles.filter(style =>
        style.name.toLowerCase().includes(searchLower) ||
        (style.description && style.description.toLowerCase().includes(searchLower)) ||
        (style.tags && style.tags.some(tag => tag.toLowerCase().includes(searchLower)))
      );
    }

    // Sort by lastUsedAt if available, then by createdAt
    styles.sort((a, b) => {
      const aLastUsed = a.lastUsedAt ? new Date(a.lastUsedAt).getTime() : new Date(a.createdAt).getTime();
      const bLastUsed = b.lastUsedAt ? new Date(b.lastUsedAt).getTime() : new Date(b.createdAt).getTime();
      return bLastUsed - aLastUsed; // Descending order
    });

    return { styles, lastVisible: lastVisibleDoc };
  } catch (error) {
    console.error('Error getting QR styles:', error);
    throw error;
  }
}

export async function getQRStyleTemplates(): Promise<QRStyle[]> {
  return getDocuments<QRStyle>(
    qrStylesCollection,
    [
      where('isTemplate', '==', true),
      orderBy('templateCategory', 'asc'),
      orderBy('createdAt', 'asc')
    ]
  );
}

export async function createQRStyle(data: Omit<QRStyle, 'id' | 'createdAt' | 'updatedAt'>): Promise<QRStyle> {
  const defaultStyle: Omit<QRStyle, 'id'> = {
    ...data,
    downloadCount: 0,
    printCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return createDocument<QRStyle>(qrStylesCollection, defaultStyle);
}

export async function updateQRStyle(styleId: string, data: Partial<QRStyle>): Promise<QRStyle> {
  const updateData = {
    ...data,
    updatedAt: new Date(),
  };

  return updateDocument<QRStyle>(qrStylesCollection, styleId, updateData);
}

export async function deleteQRStyle(styleId: string): Promise<void> {
  return deleteDocument(qrStylesCollection, styleId);
}

export async function incrementQRStyleDownloadCount(styleId: string): Promise<void> {
  const style = await getQRStyle(styleId);
  if (style) {
    await updateQRStyle(styleId, {
      downloadCount: style.downloadCount + 1,
      lastUsedAt: new Date()
    });
  }
}

export async function incrementQRStylePrintCount(styleId: string): Promise<void> {
  const style = await getQRStyle(styleId);
  if (style) {
    await updateQRStyle(styleId, {
      printCount: style.printCount + 1,
      lastUsedAt: new Date()
    });
  }
}

export async function getUserQRStyleCount(userId: string): Promise<number> {
  try {
    const constraints: QueryConstraint[] = [
      where('userId', '==', userId),
      where('isTemplate', '==', false)
    ];

    const q = query(collection(db, qrStylesCollection), ...constraints);
    const querySnapshot = await getDocs(q);

    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting QR style count:', error);
    throw error;
  }
}

export async function getRecentQRStyles(userId: string, limitCount: number = 4): Promise<QRStyle[]> {
  try {
    const constraints: QueryConstraint[] = [
      where('userId', '==', userId),
      where('isTemplate', '==', false),
      orderBy('createdAt', 'desc') // Order by creation date first
    ];

    const q = query(collection(db, qrStylesCollection), ...constraints, limit(limitCount));
    const querySnapshot = await getDocs(q);

    let styles = querySnapshot.docs.map(doc => {
      return { id: doc.id, ...convertTimestampToDate(doc.data()) } as QRStyle;
    });

    // Sort by lastUsedAt if available, then by createdAt
    styles.sort((a, b) => {
      const aLastUsed = a.lastUsedAt ? new Date(a.lastUsedAt).getTime() : new Date(a.createdAt).getTime();
      const bLastUsed = b.lastUsedAt ? new Date(b.lastUsedAt).getTime() : new Date(b.createdAt).getTime();
      return bLastUsed - aLastUsed; // Descending order
    });

    return styles;
  } catch (error) {
    console.error('Error getting recent QR styles:', error);
    throw error;
  }
}