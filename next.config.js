/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable ESLint during build for production deployment
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable TypeScript checking during build for production deployment
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    domains: ['storage.googleapis.com'],
  },
  webpack(config) {
    // Configure webpack to handle SVG files
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // Disable webpack caching to resolve build hanging issues
    config.cache = false;

    return config;
  },
};

module.exports = nextConfig;